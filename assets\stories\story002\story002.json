{"story_id": "story002", "age_group": "3-5", "difficulty": "easy", "title": "The Sharing Squirrel", "moral": "Sharing", "cover_image": "story_cover.jpg", "setup": {"setting": "A sunny forest with tall trees and colorful flowers", "tone": "Friendly and cheerful", "context": "A squirrel learns the joy of sharing with a friend"}, "background_music": "gentle_forest.mp3", "narrator_profile": {"name": "<PERSON> Storyteller", "voice": {"name": "en-US-Wavenet-C", "pitch": 1.0, "rate": 1.0, "volume": 1.0}}, "characters": [{"name": "<PERSON>", "description": "A playful squirrel with a fluffy tail", "role": "Protagonist", "voice": {"name": "en-US-Wavenet-D", "pitch": 1.2, "rate": 1.1, "volume": 0.9}}, {"name": "<PERSON>", "description": "A kind rabbit with soft, white fur", "role": "Supporting", "voice": {"name": "en-US-Wavenet-E", "pitch": 1.1, "rate": 1.0, "volume": 0.9}}], "scenes": [{"id": "scene_1", "text": "One sunny day, <PERSON> the squirrel found a big, shiny acorn in the forest.", "speaker": "narrator", "emotion": "happy", "image": "sammy_finds_acorn.jpg", "pause_duration": 2000, "next": "scene_2"}, {"id": "scene_2", "text": "<PERSON> the rabbit hopped by and saw the acorn. She looked a little sad because she didn’t have one.", "speaker": "narrator", "emotion": "curious", "image": "ruby_sees_acorn.jpg", "pause_duration": 1500, "next": "scene_3"}, {"id": "scene_3", "text": "<PERSON> noticed <PERSON>’s sad face. What should <PERSON> do?", "speaker": "narrator", "emotion": "thoughtful", "image": "choice_moment.jpg", "pause_duration": 0, "choices": [{"option": "Share the acorn with Ruby", "visual": "sharing_icon.jpg", "next": "scene_4a"}, {"option": "Keep the acorn for himself", "visual": "keeping_icon.jpg", "next": "scene_4b"}]}, {"id": "scene_4a", "text": "<PERSON> smiled and said, 'Here, <PERSON>! Let’s share this acorn together!'", "speaker": "<PERSON>", "emotion": "happy", "image": "sharing_acorn.jpg", "pause_duration": 2000, "outcome": "good", "conclusion": "<PERSON>’s eyes lit up, and they both enjoyed the acorn, laughing and playing together.", "rewards": {"completion": 1, "good": 1}, "reflection": {"text": "Why was sharing the acorn a nice thing to do?", "emotion": "curious"}}, {"id": "scene_4b", "text": "<PERSON> clutched the acorn tightly and said, 'This is mine!'", "speaker": "<PERSON>", "emotion": "selfish", "image": "sammy_keeps_acorn.jpg", "pause_duration": 2000, "outcome": "bad", "conclusion": "<PERSON> walked away with a sigh, and <PERSON> sat alone, feeling a little lonely.", "rewards": {"completion": 1, "good": 0}, "reflection": {"text": "What could <PERSON> do next time to make <PERSON> happy?", "emotion": "hopeful"}}], "post_story": {"good_outcome": {"discussion": {"text": "<PERSON> and <PERSON> had so much fun sharing! Sharing made them both happy. How does it feel when you share with a friend?", "emotion": "happy"}, "replay_prompt": {"text": "What if <PERSON> made a different choice? Let’s go back to the forest and see!", "emotion": "excited"}}, "bad_outcome": {"discussion": {"text": "Oh no, <PERSON> was sad, and <PERSON> felt lonely. Sharing could have made them both happy. Why is sharing important?", "emotion": "gentle"}, "replay_prompt": {"text": "Let’s try the story again! Maybe <PERSON> can share this time. Ready to go back to the forest?", "emotion": "hopeful"}}}}
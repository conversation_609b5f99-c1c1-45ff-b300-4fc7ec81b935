import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/core/storage/offline_storage_service_mobile.dart';
import 'package:choice_once_upon_a_time/core/audio/emotion_cue_mapper_service.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';
import 'package:choice_once_upon_a_time/shared/widgets/popups/exit_story_confirmation_popup.dart';
import 'package:choice_once_upon_a_time/shared/widgets/popups/offline_notification_popup.dart';
import 'package:choice_once_upon_a_time/shared/widgets/popups/content_update_notification_popup.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/story_cover_card_widget.dart';

void main() {
  group('Sprint 4: Full Story Integration & Offline Functionality', () {
    late OfflineStorageService offlineStorage;
    late StoryRepository storyRepository;

    setUp(() {
      offlineStorage = OfflineStorageService();
      storyRepository = StoryRepository();
    });

    group('Offline Storage Service', () {
      test('should initialize storage directory', () async {
        await offlineStorage.initDatabase();
        // Test passes if no exception is thrown
        expect(true, isTrue);
      });

      test('should check if story is downloaded', () async {
        final isDownloaded = await offlineStorage.isStoryDownloaded('test_story', '1.0.0');
        expect(isDownloaded, isFalse); // Should be false for non-existent story
      });

      test('should get all downloaded stories', () async {
        final downloadedStories = await offlineStorage.getAllDownloadedStories();
        expect(downloadedStories, isA<List<Map<String, dynamic>>>());
      });

      test('should calculate total storage used', () async {
        final storageUsed = await offlineStorage.getTotalStorageUsedMb();
        expect(storageUsed, isA<double>());
        expect(storageUsed, greaterThanOrEqualTo(0));
      });

      test('should check storage space availability', () async {
        final hasSpace = await offlineStorage.hasEnoughStorageSpace(1024 * 1024); // 1MB
        expect(hasSpace, isA<bool>());
      });
    });

    group('Enhanced Emotion Cue Mapping', () {
      test('should map story-specific emotion cues', () {
        final warmlyParams = EmotionCueMapperService.getParametersForEmotion('warmly');
        expect(warmlyParams.pitch, equals(1.0));
        expect(warmlyParams.rate, equals(0.4));
        expect(warmlyParams.volume, equals(0.85));

        final softlyParams = EmotionCueMapperService.getParametersForEmotion('softly');
        expect(softlyParams.pitch, equals(0.9));
        expect(softlyParams.rate, equals(0.35));
        expect(softlyParams.volume, equals(0.75));

        final conspiratorially = EmotionCueMapperService.getParametersForEmotion('conspiratorially');
        expect(conspiratorially.pitch, equals(0.9));
        expect(conspiratorially.rate, equals(0.35));
        expect(conspiratorially.volume, equals(0.7));
      });

      test('should blend multiple emotions', () {
        final blendedParams = EmotionCueMapperService.blendEmotions(['warmly', 'softly']);
        expect(blendedParams.pitch, closeTo(0.95, 0.01)); // Average of 1.0 and 0.9
        expect(blendedParams.rate, closeTo(0.375, 0.01)); // Average of 0.4 and 0.35
        expect(blendedParams.volume, closeTo(0.8, 0.01)); // Average of 0.85 and 0.75
      });

      test('should adjust parameters for age segments', () {
        final baseParams = EmotionCueMapperService.getParametersForEmotion('warmly');
        final adjustedFor3to5 = EmotionCueMapperService.adjustForAge(baseParams, '3-5');
        
        expect(adjustedFor3to5.rate, lessThan(baseParams.rate)); // Should be slower
        expect(adjustedFor3to5.pitch, greaterThan(baseParams.pitch)); // Should be higher
      });

      test('should provide emotion descriptions', () {
        final description = EmotionCueMapperService.getEmotionDescription('warmly');
        expect(description, isA<String>());
        expect(description.isNotEmpty, isTrue);
      });

      test('should check if emotion is supported', () {
        expect(EmotionCueMapperService.isEmotionSupported('warmly'), isTrue);
        expect(EmotionCueMapperService.isEmotionSupported('nonexistent_emotion'), isFalse);
      });
    });

    group('Story Repository with Offline Support', () {
      test('should fetch story metadata list', () async {
        final stories = await storyRepository.fetchStoryMetadataList();
        expect(stories, isA<List<StoryMetadataModel>>());
        expect(stories.isNotEmpty, isTrue);
      });

      test('should fetch story by ID from assets', () async {
        final story = await storyRepository.fetchStoryById('pip_pantry_puzzle_v1');
        expect(story, isNotNull);
        expect(story!.id, equals('pip_pantry_puzzle_v1'));
      });

      test('should handle story not found gracefully', () async {
        expect(
          () => storyRepository.fetchStoryById('nonexistent_story'),
          throwsA(isA<StoryLoadException>()),
        );
      });
    });

    group('Popup Widgets', () {
      testWidgets('Exit Story Confirmation Popup should display correctly', (WidgetTester tester) async {
        bool confirmCalled = false;
        bool cancelCalled = false;

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: ExitStoryConfirmationPopup(
                  onConfirmExit: () => confirmCalled = true,
                  onCancel: () => cancelCalled = true,
                ),
              ),
            ),
          ),
        );

        expect(find.text('Leave Our Story?'), findsOneWidget);
        expect(find.text('Stay in Story'), findsOneWidget);
        expect(find.text('Leave Story'), findsOneWidget);

        // Test cancel button
        await tester.tap(find.text('Stay in Story'));
        await tester.pumpAndSettle();
        expect(cancelCalled, isTrue);
      });

      testWidgets('Offline Notification Popup should display correctly', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: OfflineNotificationPopup(
                customMessage: 'Test offline message',
                onRetry: () {},
                onDismiss: () {},
              ),
            ),
          ),
        );

        expect(find.text('No Internet Connection'), findsOneWidget);
        expect(find.text('Test offline message'), findsOneWidget);
        expect(find.text('Try Again'), findsOneWidget);
        expect(find.text('Got It'), findsOneWidget);
      });

      testWidgets('Content Update Notification Popup should display correctly', (WidgetTester tester) async {
        const testStory = StoryMetadataModel(
          id: 'test_story',
          title: {'en-US': 'Test Story'},
          coverImageUrl: 'test_cover.png',
          loglineShort: {'en-US': 'Test logline'},
          targetMoralValue: 'Test Value',
          version: '1.0.0',
          supportedLanguages: ['en-US'],
          defaultLanguage: 'en-US',
          isFree: true,
          estimatedDurationMinutes: 5,
          targetAgeSubSegment: '4-6',
          initialSceneId: 'scene_01',
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ContentUpdateNotificationPopup(
                storyMetadata: testStory,
                currentVersion: '1.0.0',
                newVersion: '1.1.0',
                onUpdate: () {},
                onLater: () {},
                onSkip: () {},
              ),
            ),
          ),
        );

        expect(find.text('Story Update Available!'), findsOneWidget);
        expect(find.text('Test Story'), findsOneWidget);
        expect(find.text('Update Now'), findsOneWidget);
        expect(find.text('Later'), findsOneWidget);
        expect(find.text('Skip This Version'), findsOneWidget);
      });
    });

    group('Story Cover Card Widget with Download', () {
      testWidgets('should display story information correctly', (WidgetTester tester) async {
        const testStory = StoryMetadataModel(
          id: 'test_story',
          title: {'en-US': 'Test Story Title'},
          coverImageUrl: 'test_cover.png',
          loglineShort: {'en-US': 'Test logline'},
          targetMoralValue: 'Kindness',
          version: '1.0.0',
          supportedLanguages: ['en-US'],
          defaultLanguage: 'en-US',
          isFree: true,
          estimatedDurationMinutes: 8,
          targetAgeSubSegment: '4-6',
          initialSceneId: 'scene_01',
        );

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: StoryCoverCardWidget(
                  story: testStory,
                  onTap: () {},
                ),
              ),
            ),
          ),
        );

        expect(find.text('Test Story Title'), findsOneWidget);
        expect(find.text('Kindness'), findsOneWidget);
        expect(find.text('8 min'), findsOneWidget);
        expect(find.text('Ages 4-6'), findsOneWidget);
      });

      testWidgets('should show download button for unlocked stories', (WidgetTester tester) async {
        const testStory = StoryMetadataModel(
          id: 'test_story',
          title: {'en-US': 'Test Story'},
          coverImageUrl: 'test_cover.png',
          loglineShort: {'en-US': 'Test logline'},
          targetMoralValue: 'Test Value',
          version: '1.0.0',
          supportedLanguages: ['en-US'],
          defaultLanguage: 'en-US',
          isFree: true,
          estimatedDurationMinutes: 5,
          targetAgeSubSegment: '4-6',
          initialSceneId: 'scene_01',
          isLocked: false,
        );

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: StoryCoverCardWidget(
                  story: testStory,
                  onTap: () {},
                ),
              ),
            ),
          ),
        );

        // Should show download icon
        expect(find.byIcon(Icons.download_outlined), findsOneWidget);
      });
    });

    group('Error Handling', () {
      test('should handle storage exceptions gracefully', () async {
        // Test that storage exceptions are properly wrapped
        expect(
          () => throw StorageException('Test error', technicalDetails: 'Test details'),
          throwsA(isA<StorageException>()),
        );
      });

      test('should handle story load exceptions gracefully', () async {
        expect(
          () => throw StoryLoadException('Test error', technicalDetails: 'Test details'),
          throwsA(isA<StoryLoadException>()),
        );
      });
    });
  });
}

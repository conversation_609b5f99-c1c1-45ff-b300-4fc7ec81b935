import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

/// Service for managing story playback settings
class StorySettingsService {
  static final Logger _logger = Logger();
  static StorySettingsService? _instance;
  
  SharedPreferences? _prefs;
  
  // Settings keys
  static const String _subtitleSizeKey = 'subtitle_size';
  static const String _narrationSpeedKey = 'narration_speed';
  static const String _subtitlesEnabledKey = 'subtitles_enabled';
  static const String _autoPlayKey = 'auto_play';
  
  // Default values
  static const double _defaultSubtitleSize = 18.0;
  static const double _defaultNarrationSpeed = 0.5;
  static const bool _defaultSubtitlesEnabled = true;
  static const bool _defaultAutoPlay = true;
  
  // Current settings
  double _subtitleSize = _defaultSubtitleSize;
  double _narrationSpeed = _defaultNarrationSpeed;
  bool _subtitlesEnabled = _defaultSubtitlesEnabled;
  bool _autoPlay = _defaultAutoPlay;
  
  // Stream controllers for settings changes
  final StreamController<double> _subtitleSizeController = StreamController<double>.broadcast();
  final StreamController<double> _narrationSpeedController = StreamController<double>.broadcast();
  final StreamController<bool> _subtitlesEnabledController = StreamController<bool>.broadcast();
  final StreamController<bool> _autoPlayController = StreamController<bool>.broadcast();
  
  // Private constructor
  StorySettingsService._();
  
  /// Get singleton instance
  static StorySettingsService get instance {
    _instance ??= StorySettingsService._();
    return _instance!;
  }
  
  // Getters for streams
  Stream<double> get subtitleSizeStream => _subtitleSizeController.stream;
  Stream<double> get narrationSpeedStream => _narrationSpeedController.stream;
  Stream<bool> get subtitlesEnabledStream => _subtitlesEnabledController.stream;
  Stream<bool> get autoPlayStream => _autoPlayController.stream;
  
  // Getters for current values
  double get subtitleSize => _subtitleSize;
  double get narrationSpeed => _narrationSpeed;
  bool get subtitlesEnabled => _subtitlesEnabled;
  bool get autoPlay => _autoPlay;

  /// Initialize the settings service
  Future<void> initialize() async {
    try {
      _logger.i('[StorySettingsService] Initializing settings service');
      
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      
      _logger.i('[StorySettingsService] Settings service initialized successfully');
      
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to initialize settings service: $e');
      // Use default values if initialization fails
      _setDefaultValues();
    }
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    if (_prefs == null) return;
    
    _subtitleSize = _prefs!.getDouble(_subtitleSizeKey) ?? _defaultSubtitleSize;
    _narrationSpeed = _prefs!.getDouble(_narrationSpeedKey) ?? _defaultNarrationSpeed;
    _subtitlesEnabled = _prefs!.getBool(_subtitlesEnabledKey) ?? _defaultSubtitlesEnabled;
    _autoPlay = _prefs!.getBool(_autoPlayKey) ?? _defaultAutoPlay;
    
    _logger.d('[StorySettingsService] Settings loaded - Subtitle size: $_subtitleSize, Narration speed: $_narrationSpeed, Subtitles enabled: $_subtitlesEnabled, Auto play: $_autoPlay');
  }

  /// Set default values
  void _setDefaultValues() {
    _subtitleSize = _defaultSubtitleSize;
    _narrationSpeed = _defaultNarrationSpeed;
    _subtitlesEnabled = _defaultSubtitlesEnabled;
    _autoPlay = _defaultAutoPlay;
  }

  /// Update subtitle size (12.0 to 32.0)
  Future<void> setSubtitleSize(double size) async {
    final clampedSize = size.clamp(12.0, 32.0);
    
    if (_subtitleSize != clampedSize) {
      _subtitleSize = clampedSize;
      _subtitleSizeController.add(_subtitleSize);
      
      await _saveSubtitleSize();
      _logger.d('[StorySettingsService] Subtitle size updated to: $_subtitleSize');
    }
  }

  /// Update narration speed (0.1 to 1.0)
  Future<void> setNarrationSpeed(double speed) async {
    final clampedSpeed = speed.clamp(0.1, 1.0);
    
    if (_narrationSpeed != clampedSpeed) {
      _narrationSpeed = clampedSpeed;
      _narrationSpeedController.add(_narrationSpeed);
      
      await _saveNarrationSpeed();
      _logger.d('[StorySettingsService] Narration speed updated to: $_narrationSpeed');
    }
  }

  /// Toggle subtitles enabled/disabled
  Future<void> setSubtitlesEnabled(bool enabled) async {
    if (_subtitlesEnabled != enabled) {
      _subtitlesEnabled = enabled;
      _subtitlesEnabledController.add(_subtitlesEnabled);
      
      await _saveSubtitlesEnabled();
      _logger.d('[StorySettingsService] Subtitles enabled updated to: $_subtitlesEnabled');
    }
  }

  /// Toggle auto play enabled/disabled
  Future<void> setAutoPlay(bool enabled) async {
    if (_autoPlay != enabled) {
      _autoPlay = enabled;
      _autoPlayController.add(_autoPlay);

      await _saveAutoPlay();
      _logger.d('[StorySettingsService] Auto play updated to: $_autoPlay');
    }
  }

  /// Set auto scene progression (alias for autoPlay for clarity)
  Future<void> setAutoSceneProgression(bool enabled) async {
    await setAutoPlay(enabled);
  }

  /// Get auto scene progression setting
  bool get autoSceneProgression => _autoPlay;

  /// Save subtitle size to SharedPreferences
  Future<void> _saveSubtitleSize() async {
    try {
      await _prefs?.setDouble(_subtitleSizeKey, _subtitleSize);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save subtitle size: $e');
    }
  }

  /// Save narration speed to SharedPreferences
  Future<void> _saveNarrationSpeed() async {
    try {
      await _prefs?.setDouble(_narrationSpeedKey, _narrationSpeed);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save narration speed: $e');
    }
  }

  /// Save subtitles enabled to SharedPreferences
  Future<void> _saveSubtitlesEnabled() async {
    try {
      await _prefs?.setBool(_subtitlesEnabledKey, _subtitlesEnabled);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save subtitles enabled: $e');
    }
  }

  /// Save auto play to SharedPreferences
  Future<void> _saveAutoPlay() async {
    try {
      await _prefs?.setBool(_autoPlayKey, _autoPlay);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save auto play: $e');
    }
  }

  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    _logger.i('[StorySettingsService] Resetting all settings to defaults');
    
    await setSubtitleSize(_defaultSubtitleSize);
    await setNarrationSpeed(_defaultNarrationSpeed);
    await setSubtitlesEnabled(_defaultSubtitlesEnabled);
    await setAutoPlay(_defaultAutoPlay);
    
    _logger.i('[StorySettingsService] All settings reset to defaults');
  }

  /// Get settings as a map for debugging
  Map<String, dynamic> getSettingsMap() {
    return {
      'subtitleSize': _subtitleSize,
      'narrationSpeed': _narrationSpeed,
      'subtitlesEnabled': _subtitlesEnabled,
      'autoPlay': _autoPlay,
    };
  }

  /// Dispose of resources
  void dispose() {
    _subtitleSizeController.close();
    _narrationSpeedController.close();
    _subtitlesEnabledController.close();
    _autoPlayController.close();
    _logger.i('[StorySettingsService] Service disposed');
  }
}

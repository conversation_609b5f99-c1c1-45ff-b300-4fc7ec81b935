import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

/// Service for managing story playback settings
class StorySettingsService {
  static final Logger _logger = Logger();
  static StorySettingsService? _instance;
  
  SharedPreferences? _prefs;
  
  // Settings keys
  static const String _subtitleSizeKey = 'subtitle_size';
  static const String _narrationSpeedKey = 'narration_speed';
  static const String _subtitlesEnabledKey = 'subtitles_enabled';
  static const String _autoPlayKey = 'auto_play';
  static const String _controlRowTransparencyKey = 'control_row_transparency';
  static const String _textFontSizeKey = 'text_font_size';

  // Default values
  static const double _defaultSubtitleSize = 18.0;
  static const double _defaultNarrationSpeed = 0.5;
  static const bool _defaultSubtitlesEnabled = true;
  static const bool _defaultAutoPlay = true;
  static const double _defaultControlRowTransparency = 0.5; // 50%
  static const double _defaultTextFontSize = 18.0;
  
  // Current settings
  double _subtitleSize = _defaultSubtitleSize;
  double _narrationSpeed = _defaultNarrationSpeed;
  bool _subtitlesEnabled = _defaultSubtitlesEnabled;
  bool _autoPlay = _defaultAutoPlay;
  double _controlRowTransparency = _defaultControlRowTransparency;
  double _textFontSize = _defaultTextFontSize;
  
  // Stream controllers for settings changes
  final StreamController<double> _subtitleSizeController = StreamController<double>.broadcast();
  final StreamController<double> _narrationSpeedController = StreamController<double>.broadcast();
  final StreamController<bool> _subtitlesEnabledController = StreamController<bool>.broadcast();
  final StreamController<bool> _autoPlayController = StreamController<bool>.broadcast();
  final StreamController<double> _controlRowTransparencyController = StreamController<double>.broadcast();
  final StreamController<double> _textFontSizeController = StreamController<double>.broadcast();
  
  // Private constructor
  StorySettingsService._();
  
  /// Get singleton instance
  static StorySettingsService get instance {
    _instance ??= StorySettingsService._();
    return _instance!;
  }
  
  // Getters for streams
  Stream<double> get subtitleSizeStream => _subtitleSizeController.stream;
  Stream<double> get narrationSpeedStream => _narrationSpeedController.stream;
  Stream<bool> get subtitlesEnabledStream => _subtitlesEnabledController.stream;
  Stream<bool> get autoPlayStream => _autoPlayController.stream;
  Stream<double> get controlRowTransparencyStream => _controlRowTransparencyController.stream;
  Stream<double> get textFontSizeStream => _textFontSizeController.stream;

  // Getters for current values
  double get subtitleSize => _subtitleSize;
  double get narrationSpeed => _narrationSpeed;
  bool get subtitlesEnabled => _subtitlesEnabled;
  bool get autoPlay => _autoPlay;
  double get controlRowTransparency => _controlRowTransparency;
  double get textFontSize => _textFontSize;

  /// Initialize the settings service
  Future<void> initialize() async {
    try {
      _logger.i('[StorySettingsService] Initializing settings service');
      
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      
      _logger.i('[StorySettingsService] Settings service initialized successfully');
      
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to initialize settings service: $e');
      // Use default values if initialization fails
      _setDefaultValues();
    }
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    if (_prefs == null) return;

    _subtitleSize = _prefs!.getDouble(_subtitleSizeKey) ?? _defaultSubtitleSize;
    _narrationSpeed = _prefs!.getDouble(_narrationSpeedKey) ?? _defaultNarrationSpeed;
    _subtitlesEnabled = _prefs!.getBool(_subtitlesEnabledKey) ?? _defaultSubtitlesEnabled;
    _autoPlay = _prefs!.getBool(_autoPlayKey) ?? _defaultAutoPlay;
    _controlRowTransparency = _prefs!.getDouble(_controlRowTransparencyKey) ?? _defaultControlRowTransparency;
    _textFontSize = _prefs!.getDouble(_textFontSizeKey) ?? _defaultTextFontSize;

    _logger.d('[StorySettingsService] Settings loaded - Subtitle size: $_subtitleSize, Narration speed: $_narrationSpeed, Subtitles enabled: $_subtitlesEnabled, Auto play: $_autoPlay, Control transparency: $_controlRowTransparency, Text font size: $_textFontSize');
  }

  /// Set default values
  void _setDefaultValues() {
    _subtitleSize = _defaultSubtitleSize;
    _narrationSpeed = _defaultNarrationSpeed;
    _subtitlesEnabled = _defaultSubtitlesEnabled;
    _autoPlay = _defaultAutoPlay;
    _controlRowTransparency = _defaultControlRowTransparency;
    _textFontSize = _defaultTextFontSize;
  }

  /// Update subtitle size (12.0 to 32.0)
  Future<void> setSubtitleSize(double size) async {
    final clampedSize = size.clamp(12.0, 32.0);
    
    if (_subtitleSize != clampedSize) {
      _subtitleSize = clampedSize;
      _subtitleSizeController.add(_subtitleSize);
      
      await _saveSubtitleSize();
      _logger.d('[StorySettingsService] Subtitle size updated to: $_subtitleSize');
    }
  }

  /// Update narration speed (0.1 to 1.0)
  Future<void> setNarrationSpeed(double speed) async {
    final clampedSpeed = speed.clamp(0.1, 1.0);
    
    if (_narrationSpeed != clampedSpeed) {
      _narrationSpeed = clampedSpeed;
      _narrationSpeedController.add(_narrationSpeed);
      
      await _saveNarrationSpeed();
      _logger.d('[StorySettingsService] Narration speed updated to: $_narrationSpeed');
    }
  }

  /// Toggle subtitles enabled/disabled
  Future<void> setSubtitlesEnabled(bool enabled) async {
    if (_subtitlesEnabled != enabled) {
      _subtitlesEnabled = enabled;
      _subtitlesEnabledController.add(_subtitlesEnabled);
      
      await _saveSubtitlesEnabled();
      _logger.d('[StorySettingsService] Subtitles enabled updated to: $_subtitlesEnabled');
    }
  }

  /// Toggle auto play enabled/disabled
  Future<void> setAutoPlay(bool enabled) async {
    if (_autoPlay != enabled) {
      _autoPlay = enabled;
      _autoPlayController.add(_autoPlay);

      await _saveAutoPlay();
      _logger.d('[StorySettingsService] Auto play updated to: $_autoPlay');
    }
  }

  /// Set auto scene progression (alias for autoPlay for clarity)
  Future<void> setAutoSceneProgression(bool enabled) async {
    await setAutoPlay(enabled);
  }

  /// Get auto scene progression setting
  bool get autoSceneProgression => _autoPlay;

  /// Update control row transparency (0.0 to 1.0)
  Future<void> setControlRowTransparency(double transparency) async {
    final clampedTransparency = transparency.clamp(0.0, 1.0);

    if (_controlRowTransparency != clampedTransparency) {
      _controlRowTransparency = clampedTransparency;
      _controlRowTransparencyController.add(_controlRowTransparency);

      await _saveControlRowTransparency();
      _logger.d('[StorySettingsService] Control row transparency updated to: $_controlRowTransparency');
    }
  }

  /// Update text font size (12.0 to 32.0)
  Future<void> setTextFontSize(double size) async {
    final clampedSize = size.clamp(12.0, 32.0);

    if (_textFontSize != clampedSize) {
      _textFontSize = clampedSize;
      _textFontSizeController.add(_textFontSize);

      await _saveTextFontSize();
      _logger.d('[StorySettingsService] Text font size updated to: $_textFontSize');
    }
  }

  /// Save subtitle size to SharedPreferences
  Future<void> _saveSubtitleSize() async {
    try {
      await _prefs?.setDouble(_subtitleSizeKey, _subtitleSize);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save subtitle size: $e');
    }
  }

  /// Save narration speed to SharedPreferences
  Future<void> _saveNarrationSpeed() async {
    try {
      await _prefs?.setDouble(_narrationSpeedKey, _narrationSpeed);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save narration speed: $e');
    }
  }

  /// Save subtitles enabled to SharedPreferences
  Future<void> _saveSubtitlesEnabled() async {
    try {
      await _prefs?.setBool(_subtitlesEnabledKey, _subtitlesEnabled);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save subtitles enabled: $e');
    }
  }

  /// Save auto play to SharedPreferences
  Future<void> _saveAutoPlay() async {
    try {
      await _prefs?.setBool(_autoPlayKey, _autoPlay);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save auto play: $e');
    }
  }

  /// Save control row transparency to SharedPreferences
  Future<void> _saveControlRowTransparency() async {
    try {
      await _prefs?.setDouble(_controlRowTransparencyKey, _controlRowTransparency);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save control row transparency: $e');
    }
  }

  /// Save text font size to SharedPreferences
  Future<void> _saveTextFontSize() async {
    try {
      await _prefs?.setDouble(_textFontSizeKey, _textFontSize);
    } catch (e) {
      _logger.e('[StorySettingsService] Failed to save text font size: $e');
    }
  }

  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    _logger.i('[StorySettingsService] Resetting all settings to defaults');

    await setSubtitleSize(_defaultSubtitleSize);
    await setNarrationSpeed(_defaultNarrationSpeed);
    await setSubtitlesEnabled(_defaultSubtitlesEnabled);
    await setAutoPlay(_defaultAutoPlay);
    await setControlRowTransparency(_defaultControlRowTransparency);
    await setTextFontSize(_defaultTextFontSize);

    _logger.i('[StorySettingsService] All settings reset to defaults');
  }

  /// Get settings as a map for debugging
  Map<String, dynamic> getSettingsMap() {
    return {
      'subtitleSize': _subtitleSize,
      'narrationSpeed': _narrationSpeed,
      'subtitlesEnabled': _subtitlesEnabled,
      'autoPlay': _autoPlay,
      'controlRowTransparency': _controlRowTransparency,
      'textFontSize': _textFontSize,
    };
  }

  /// Dispose of resources
  void dispose() {
    _subtitleSizeController.close();
    _narrationSpeedController.close();
    _subtitlesEnabledController.close();
    _autoPlayController.close();
    _controlRowTransparencyController.close();
    _textFontSizeController.close();
    _logger.i('[StorySettingsService] Service disposed');
  }
}

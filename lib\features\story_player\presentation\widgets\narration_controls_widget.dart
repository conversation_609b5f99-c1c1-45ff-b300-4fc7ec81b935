import 'package:flutter/material.dart';

/// Widget for controlling story narration playback
class NarrationControlsWidget extends StatelessWidget {
  final bool isPlaying;
  final VoidCallback onPlayPause;
  final VoidCallback onReplay;
  final VoidCallback? onPrevious;
  final VoidCallback? onNext;

  const NarrationControlsWidget({
    super.key,
    required this.isPlaying,
    required this.onPlayPause,
    required this.onReplay,
    this.onPrevious,
    this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;

    // Responsive sizing
    final isSmallScreen = screenSize.width < 600;
    final buttonSize = isSmallScreen ? 40.0 : 48.0;
    final primaryButtonSize = isSmallScreen ? 56.0 : 64.0;
    final iconSize = isSmallScreen ? 20.0 : 24.0;
    final primaryIconSize = isSmallScreen ? 28.0 : 32.0;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: screenSize.width * 0.05, // 5% of screen width
        vertical: isSmallScreen ? 12.0 : 16.0,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Previous button (if available)
          if (onPrevious != null)
            _buildControlButton(
              icon: Icons.skip_previous,
              onPressed: onPrevious!,
              tooltip: 'Previous',
              theme: theme,
              size: buttonSize,
              iconSize: iconSize,
            )
          else
            SizedBox(width: buttonSize), // Placeholder for spacing

          // Replay button
          _buildControlButton(
            icon: Icons.replay,
            onPressed: onReplay,
            tooltip: 'Replay',
            theme: theme,
            size: buttonSize,
            iconSize: iconSize,
          ),

          // Play/Pause button (larger, primary)
          _buildPrimaryControlButton(
            icon: isPlaying ? Icons.pause : Icons.play_arrow,
            onPressed: onPlayPause,
            tooltip: isPlaying ? 'Pause' : 'Play',
            theme: theme,
            size: primaryButtonSize,
            iconSize: primaryIconSize,
          ),

          // Next button (if available)
          if (onNext != null)
            _buildControlButton(
              icon: Icons.skip_next,
              onPressed: onNext!,
              tooltip: 'Next',
              theme: theme,
              size: buttonSize,
              iconSize: iconSize,
            )
          else
            SizedBox(width: buttonSize), // Placeholder for spacing
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    required ThemeData theme,
    required double size,
    required double iconSize,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(size / 2),
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(size / 2),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.onSurface,
              size: iconSize,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPrimaryControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    required ThemeData theme,
    required double size,
    required double iconSize,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(size / 2),
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(size / 2),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.onPrimary,
              size: iconSize,
            ),
          ),
        ),
      ),
    );
  }
}

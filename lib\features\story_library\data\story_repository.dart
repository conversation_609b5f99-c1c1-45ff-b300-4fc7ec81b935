import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/core/services/asset_only_story_service.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_service.dart';
import 'package:logger/logger.dart';

/// Exception thrown when story loading fails
class StoryLoadException implements Exception {
  final String message;
  final String storyId;
  
  StoryLoadException(this.message, this.storyId);
  
  @override
  String toString() => 'StoryLoadException: $message (Story ID: $storyId)';
}

/// Repository for managing stories from assets (both legacy and enhanced)
class StoryRepository {
  static final Logger _logger = Logger();
  final AssetOnlyStoryService _assetStoryService;
  final EnhancedStoryService _enhancedStoryService;

  StoryRepository({
    AssetOnlyStoryService? assetStoryService,
    EnhancedStoryService? enhancedStoryService,
  }) : _assetStoryService = assetStoryService ?? AssetOnlyStoryService(),
       _enhancedStoryService = enhancedStoryService ?? EnhancedStoryService();

  /// Fetches all story metadata from assets (both legacy and enhanced)
  Future<List<StoryMetadataModel>> fetchStoryMetadataList() async {
    try {
      _logger.i('[StoryRepository] Fetching story metadata from all sources');

      // Get stories from both services
      final legacyStories = await _assetStoryService.getAllStoryMetadata();
      final enhancedStories = await _enhancedStoryService.getAllStoryMetadata();

      // Deduplicate stories by ID, preferring enhanced versions
      final storyMap = <String, StoryMetadataModel>{};

      // Add legacy stories first
      for (final story in legacyStories) {
        storyMap[story.id] = story;
      }

      // Add enhanced stories, which will override legacy versions with same ID
      for (final story in enhancedStories) {
        storyMap[story.id] = story;
      }

      final allStories = storyMap.values.toList();

      _logger.i('[StoryRepository] Total stories available: ${allStories.length} (${legacyStories.length} legacy + ${enhancedStories.length} enhanced, deduplicated from ${legacyStories.length + enhancedStories.length})');
      return allStories;

    } catch (e) {
      _logger.e('[StoryRepository] Failed to fetch story metadata: $e');
      return [];
    }
  }

  /// Fetches a specific story by ID with optional dataSource parameter
  Future<StoryModel> fetchStoryById(String storyId, {String? dataSource}) async {
    try {
      _logger.i('[StoryRepository] Fetching story: $storyId (dataSource: $dataSource)');

      final story = await _assetStoryService.loadStory(storyId);

      if (story == null) {
        throw StoryLoadException('Story not found in assets', storyId);
      }

      _logger.i('[StoryRepository] Successfully loaded story: $storyId');
      return story;

    } catch (e) {
      _logger.e('[StoryRepository] Failed to load story $storyId: $e');
      if (e is StoryLoadException) {
        rethrow;
      }
      throw StoryLoadException('Failed to load story from assets: $e', storyId);
    }
  }

  /// Checks if a story exists in assets
  Future<bool> storyExists(String storyId) async {
    try {
      final story = await _assetStoryService.loadStory(storyId);
      return story != null;
    } catch (e) {
      return false;
    }
  }

  /// Clears all caches
  void clearCache() {
    _assetStoryService.clearCache();
    _enhancedStoryService.clearCache();
    _logger.i('[StoryRepository] All caches cleared');
  }

  /// Gets cache statistics
  Map<String, dynamic> getCacheStats() {
    final enhancedStats = _enhancedStoryService.getCacheStats();
    return {
      'repository': 'StoryRepository',
      'sources': ['assets_only', 'enhanced_assets'],
      'enhanced_service': enhancedStats,
    };
  }
}

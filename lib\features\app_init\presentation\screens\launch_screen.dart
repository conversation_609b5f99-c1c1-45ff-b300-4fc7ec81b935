import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator_widget.dart';
import 'package:choice_once_upon_a_time/app/providers/settings_provider.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';

class LaunchScreen extends ConsumerStatefulWidget {
  const LaunchScreen({super.key});

  @override
  ConsumerState<LaunchScreen> createState() => _LaunchScreenState();
}

class _LaunchScreenState extends ConsumerState<LaunchScreen> {
  @override
  void initState() {
    super.initState();
    // Use WidgetsBinding to ensure the build context is available before navigating.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAndNavigate();
    });
  }

  Future<void> _initializeAndNavigate() async {
    // 1. Wait for essential services to initialize.
    // This provider holds the core app setup logic.
    await ref.read(servicesInitializationProvider.future);

    // Add a small delay for aesthetic purposes, allowing the user to see the splash screen.
    await Future.delayed(const Duration(seconds: 2));

    // Ensure the widget is still in the tree before attempting to navigate.
    if (!mounted) return;

    // 2. Determine the user's status.
    final ftueCompleted = ref.read(ftueCompletedProvider);

    // 3. Navigate to the appropriate screen.
    // We use context.go() to replace the launch screen in the navigation stack,
    // so the user can't press "back" to get to it.
    if (ftueCompleted) {
      context.go('/home');
    } else {
      context.go('/ftue');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo/Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.auto_stories,
                size: 64,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 32),
            // App Title
            Text(
              'Choice: Once Upon A Time',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 48),
            const LoadingIndicatorWidget(),
            const SizedBox(height: 24),
            Text(
              'Loading your adventures...',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

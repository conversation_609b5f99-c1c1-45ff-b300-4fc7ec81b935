import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/enhanced_story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_rewards_service.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/welcome_screen_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/character_profiles_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/enhanced_landscape_story_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_completion_widget.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Enhanced story player with welcome screen, character profiles, and advanced features
class EnhancedStoryPlayerScreen extends StatefulWidget {
  final String storyId;

  const EnhancedStoryPlayerScreen({
    super.key,
    required this.storyId,
  });

  @override
  State<EnhancedStoryPlayerScreen> createState() => _EnhancedStoryPlayerScreenState();
}

class _EnhancedStoryPlayerScreenState extends State<EnhancedStoryPlayerScreen>
    with TickerProviderStateMixin {
  // Services
  late final EnhancedStoryRepository _repository;
  late final StoryNarrationService _narrationService;
  late final StorySettingsService _settingsService;
  late final StoryRewardsService _rewardsService;

  // Animation controllers
  late final AnimationController _fadeController;
  late final AnimationController _slideController;
  late final Animation<double> _fadeAnimation;
  late final Animation<Offset> _slideAnimation;

  // State
  EnhancedStoryModel? _story;
  EnhancedSceneModel? _currentScene;
  StoryPlayerPhase _currentPhase = StoryPlayerPhase.loading;
  String? _error;
  List<String> _visitedScenes = [];
  Map<String, dynamic> _storyProgress = {};
  bool _isStoryActive = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _loadStory();
  }

  /// Initialize all services
  void _initializeServices() {
    _repository = EnhancedStoryRepository();
    // Use singleton instance to avoid TTS conflicts
    _narrationService = StoryNarrationService.instance;
    _settingsService = StorySettingsService.instance;
    _rewardsService = StoryRewardsService.instance;
  }

  /// Initialize animations
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
  }

  /// Load the story and initialize services
  Future<void> _loadStory() async {
    try {
      setState(() {
        _currentPhase = StoryPlayerPhase.loading;
        _error = null;
      });

      // Initialize services
      await Future.wait([
        _settingsService.initialize(),
        _rewardsService.initialize(),
        _narrationService.initialize(),
      ]);

      // Load the story
      final story = await _repository.fetchEnhancedStoryById(widget.storyId);
      
      setState(() {
        _story = story;
        _currentScene = story.initialScene;
        _currentPhase = StoryPlayerPhase.welcome;
      });

      // Start fade-in animation
      _fadeController.forward();

    } catch (e) {
      setState(() {
        _error = 'Failed to load story: $e';
        _currentPhase = StoryPlayerPhase.error;
      });
    }
  }

  /// Transition to character profiles phase
  Future<void> _showCharacterProfiles() async {
    await _transitionToPhase(StoryPlayerPhase.characterProfiles);
  }

  /// Start the main story
  Future<void> _startStory() async {
    // Set landscape orientation when starting story narration
    await _setLandscapeOrientation();
    await _transitionToPhase(StoryPlayerPhase.story);
  }

  /// Navigate to a specific scene with proper transition control
  Future<void> _navigateToScene(String sceneId) async {
    final scene = _story?.getSceneById(sceneId);
    if (scene != null) {
      AppLogger.debug('Hello [SCENE_DEBUG] Scene transition: ${_currentScene?.id} → $sceneId');

      setState(() {
        _currentScene = scene;
        if (!_visitedScenes.contains(sceneId)) {
          _visitedScenes.add(sceneId);
        }
      });

      // Trigger scene transition animation
      await _slideController.forward();
      _slideController.reset();

      AppLogger.debug('[SCENE_DEBUG] Scene $sceneId loaded - Image: ${scene.getImagePath(widget.storyId)}');
    }
  }

  /// Handle choice selection
  Future<void> _onChoiceSelected(ChoiceOptionModel choice) async {
    // Award choice reward
    if (_story != null && _currentScene != null) {
      await _rewardsService.awardChoiceReward(
        _story!.storyId,
        choice.option,
        choice.option,
        _story!.moral,
      );
    }

    // Navigate to next scene
    await _navigateToScene(choice.next);
    
    // Check if story is complete
    if (_currentScene?.isEnding == true) {
      await _completeStory();
    }
  }

  /// Complete the story
  Future<void> _completeStory() async {
    if (_story != null) {
      await _rewardsService.completeStory(
        _story!.storyId,
        _story!.title,
        _story!.moral,
      );
    }
    
    await _transitionToPhase(StoryPlayerPhase.completion);
  }

  /// Transition between phases with animation
  Future<void> _transitionToPhase(StoryPlayerPhase newPhase) async {
    await _fadeController.reverse();
    
    setState(() {
      _currentPhase = newPhase;
    });
    
    await _fadeController.forward();
  }



  /// Restart the story
  Future<void> _restartStory() async {
    setState(() {
      _currentScene = _story?.initialScene;
      _visitedScenes.clear();
      _storyProgress.clear();
    });
    
    await _transitionToPhase(StoryPlayerPhase.welcome);
  }

  /// Set landscape orientation for story playback
  Future<void> _setLandscapeOrientation() async {
    if (!_isStoryActive) {
      _isStoryActive = true;
      AppLogger.debug('[SCENE_DEBUG] Setting landscape orientation for story playback');
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }

  /// Reset orientation to allow all orientations
  Future<void> _resetOrientation() async {
    if (_isStoryActive) {
      _isStoryActive = false;
      AppLogger.debug('[SCENE_DEBUG] Resetting orientation to allow all orientations');
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }

  @override
  void dispose() {
    // Reset orientation when leaving story player
    _resetOrientation();
    _fadeController.dispose();
    _slideController.dispose();
    _narrationService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Remove AppBar for immersive cinema-like experience
    return Scaffold(
      body: _buildBody(),
    );
  }



  /// Build the main body
  Widget _buildBody() {
    if (_currentPhase == StoryPlayerPhase.loading) {
      return const Center(child: LoadingIndicator());
    }

    if (_currentPhase == StoryPlayerPhase.error) {
      return _buildErrorWidget();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: _buildPhaseContent(),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            _error ?? 'An error occurred',
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadStory,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Build content for current phase
  Widget _buildPhaseContent() {
    switch (_currentPhase) {
      case StoryPlayerPhase.welcome:
        return WelcomeScreenWidget(
          story: _story!,
          onContinue: _showCharacterProfiles,
          narrationService: _narrationService,
        );
      
      case StoryPlayerPhase.characterProfiles:
        return CharacterProfilesWidget(
          story: _story!,
          onContinue: _startStory,
          narrationService: _narrationService,
        );
      
      case StoryPlayerPhase.story:
        return SlideTransition(
          position: _slideAnimation,
          child: EnhancedLandscapeStoryWidget(
            story: _story!,
            scene: _currentScene!,
            onChoiceSelected: _onChoiceSelected,
            onSceneComplete: () async {
              if (_currentScene?.next != null) {
                await _navigateToScene(_currentScene!.next!);
              } else {
                await _completeStory();
              }
            },
            narrationService: _narrationService,
            settingsService: _settingsService,
          ),
        );
      
      case StoryPlayerPhase.completion:
        return StoryCompletionWidget(
          story: _story!,
          visitedScenes: _visitedScenes,
          onRestart: _restartStory,
          onExit: () => Navigator.of(context).pop(),
          rewardsService: _rewardsService,
        );
      
      default:
        return const SizedBox.shrink();
    }
  }
}

/// Enum for different phases of story playback
enum StoryPlayerPhase {
  loading,
  error,
  welcome,
  characterProfiles,
  story,
  completion,
}



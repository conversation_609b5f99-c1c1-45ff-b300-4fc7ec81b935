rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Public read for stories and app config
    match /stories/{storyId} {
      allow read: if true;
      allow write: if false; // Admin only
      match /scenes/{sceneId} {
        allow read: if true;
        allow write: if false; // Admin only
      }
    }
    
    match /app_config/global_settings {
      allow read: if true;
      allow write: if false; // Admin only
    }

    // Users can only read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow admin to read user data for support, etc. (implement via custom claims or trusted functions)
    // This would require additional setup for admin authentication
  }
}

rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Public read for all story assets and UI assets
    match /stories/{storyId}/{version}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true; // Admin only writes
    }
    
    match /story_covers/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    match /ui_assets/global/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // User-specific private files (if any in future)
    // match /users/{userId}/{allPaths=**} {
    //   allow read, write: if request.auth != null && request.auth.uid == userId;
    // }
  }
}

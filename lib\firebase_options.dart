// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDZDJ5F-FstvSm_EH_CxevA_4NHmP99CDw',
    appId: '1:29888182727:web:8498ee6e6bd650e66801e2',
    messagingSenderId: '29888182727',
    projectId: 'choice-tales',
    authDomain: 'choice-tales.firebaseapp.com',
    storageBucket: 'choice-tales.firebasestorage.app',
    measurementId: 'G-L5K4N7MXMG',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCIJkBkj7hce4xiVr-m3HJPuiTtFSwW4yU',
    appId: '1:29888182727:android:3abbbdc902f32cf06801e2',
    messagingSenderId: '29888182727',
    projectId: 'choice-tales',
    storageBucket: 'choice-tales.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDDNBJhQflSeI33gOApdvJF64ngepdVM_I',
    appId: '1:29888182727:ios:c40118b8a45f57986801e2',
    messagingSenderId: '29888182727',
    projectId: 'choice-tales',
    storageBucket: 'choice-tales.firebasestorage.app',
    iosBundleId: 'com.example.choiceOnceUponATime',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDDNBJhQflSeI33gOApdvJF64ngepdVM_I',
    appId: '1:29888182727:ios:c40118b8a45f57986801e2',
    messagingSenderId: '29888182727',
    projectId: 'choice-tales',
    storageBucket: 'choice-tales.firebasestorage.app',
    iosBundleId: 'com.example.choiceOnceUponATime',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDZDJ5F-FstvSm_EH_CxevA_4NHmP99CDw',
    appId: '1:29888182727:web:d6f167687ffcc8c96801e2',
    messagingSenderId: '29888182727',
    projectId: 'choice-tales',
    authDomain: 'choice-tales.firebaseapp.com',
    storageBucket: 'choice-tales.firebasestorage.app',
    measurementId: 'G-T2EX8TSWE5',
  );

}
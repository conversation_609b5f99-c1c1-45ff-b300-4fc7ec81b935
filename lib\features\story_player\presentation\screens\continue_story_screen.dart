import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/providers/story_library_provider.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/story_cover_card_widget.dart';
import 'package:choice_once_upon_a_time/app/providers/settings_provider.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator_widget.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

/// Screen for displaying stories that can be continued from where the user left off
class ContinueStoryScreen extends ConsumerStatefulWidget {
  const ContinueStoryScreen({super.key});

  @override
  ConsumerState<ContinueStoryScreen> createState() => _ContinueStoryScreenState();
}

class _ContinueStoryScreenState extends ConsumerState<ContinueStoryScreen> {
  @override
  void initState() {
    super.initState();
    // Load stories with progress when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(storyLibraryProvider.notifier).loadStoriesWithProgress();
    });
  }

  void _onStoryTap(BuildContext context, StoryMetadataModel story) {
    // Navigate to story player with continue flag
    context.go('/story/${story.id}?dataSource=${story.dataSource}&continue=true');
  }

  Future<void> _onRefresh() async {
    await ref.read(storyLibraryProvider.notifier).loadStoriesWithProgress();
  }

  @override
  Widget build(BuildContext context) {
    final storyLibraryState = ref.watch(storyLibraryProvider);
    final languageCode = ref.watch(narrationLanguageProvider);
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    // Filter stories that have progress
    final storiesWithProgress = storyLibraryState.stories
        .where((story) => story.hasProgress == true)
        .toList();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Continue Reading',
          style: theme.textTheme.titleLarge?.copyWith(
            fontSize: isSmallScreen ? 18 : 20,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
          tooltip: 'Back to previous screen',
        ),
      ),
      body: _buildBody(context, storyLibraryState, storiesWithProgress, languageCode),
    );
  }

  Widget _buildBody(
    BuildContext context,
    StoryLibraryState state,
    List<StoryMetadataModel> stories,
    String languageCode,
  ) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    if (state.isLoading && stories.isEmpty) {
      return const Center(child: LoadingIndicatorWidget());
    }

    if (state.error != null && stories.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(screenSize.width * 0.08),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: isSmallScreen ? 48 : 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading stories: ${state.error}',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.error,
                  fontSize: isSmallScreen ? 14 : 16,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _onRefresh,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      color: theme.colorScheme.primary,
      child: stories.isEmpty
          ? _buildEmptyState(context)
          : _buildStoriesGrid(context, stories, languageCode),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Container(
        height: screenSize.height * 0.6,
        padding: EdgeInsets.all(screenSize.width * 0.08),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.auto_stories_outlined,
              size: isSmallScreen ? 64 : 80,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 24),
            Text(
              'No Stories in Progress',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
                fontSize: isSmallScreen ? 20 : 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Start reading a story from the library to see it here when you want to continue.',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontSize: isSmallScreen ? 14 : 16,
              ),
              textAlign: TextAlign.center,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => context.go('/story_library'),
              icon: const Icon(Icons.library_books),
              label: const Text('Browse Stories'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 20 : 24,
                  vertical: isSmallScreen ? 12 : 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoriesGrid(
    BuildContext context,
    List<StoryMetadataModel> stories,
    String languageCode,
  ) {
    final screenSize = MediaQuery.of(context).size;

    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        int crossAxisCount;
        double childAspectRatio;
        double spacing;

        if (screenWidth > 1200) {
          crossAxisCount = 4;
          childAspectRatio = 0.8;
          spacing = 20.0;
        } else if (screenWidth > 800) {
          crossAxisCount = 3;
          childAspectRatio = 0.75;
          spacing = 18.0;
        } else if (screenWidth > 500) {
          crossAxisCount = 2;
          childAspectRatio = 0.7;
          spacing = 16.0;
        } else {
          // Small screens
          crossAxisCount = 1;
          childAspectRatio = 1.2;
          spacing = 16.0;
        }

        return GridView.builder(
          padding: EdgeInsets.fromLTRB(
            screenSize.width * 0.04, // 4% horizontal padding
            16.0,
            screenSize.width * 0.04,
            16.0,
          ),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: stories.length,
          itemBuilder: (context, index) {
            final story = stories[index];
            return StoryCoverCardWidget(
              story: story,
              languageCode: languageCode,
              onTap: () => _onStoryTap(context, story),
              isOneColumn: crossAxisCount == 1,
              showProgress: true, // Show progress indicator
            );
          },
        );
      },
    );
  }
}

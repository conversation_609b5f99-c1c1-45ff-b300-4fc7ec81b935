import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/scene_model.dart';

/// Widget for displaying narrator text with emotion cues
class NarratorTextDisplayWidget extends StatelessWidget {
  final SceneModel scene;
  final int currentSegmentIndex;
  final String language;

  const NarratorTextDisplayWidget({
    super.key,
    required this.scene,
    this.currentSegmentIndex = 0,
    this.language = 'en-US',
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    if (scene.narratorSegments.isEmpty) {
      return Center(
        child: Text(
          'No narrator text available',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      );
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current segment display
          _buildCurrentSegment(theme),
          
          // Segment progress indicator
          if (scene.narratorSegments.length > 1) ...[
            const SizedBox(height: 16),
            _buildSegmentProgress(theme),
          ],
          
          // Debug: Show emotion cue
          if (scene.narratorSegments.isNotEmpty && 
              currentSegmentIndex < scene.narratorSegments.length) ...[
            const SizedBox(height: 12),
            _buildEmotionCueDebug(theme),
          ],
        ],
      ),
    );
  }

  Widget _buildCurrentSegment(ThemeData theme) {
    if (currentSegmentIndex >= scene.narratorSegments.length) {
      return Text(
        'Segment not available',
        style: theme.textTheme.bodyLarge?.copyWith(
          color: Colors.grey[600],
        ),
      );
    }

    final currentSegment = scene.narratorSegments[currentSegmentIndex];
    final text = currentSegment.getLocalizedText(language);

    return Expanded(
      child: SingleChildScrollView(
        child: Text(
          text,
          style: theme.textTheme.headlineSmall?.copyWith(
            height: 1.4,
            color: const Color(0xFF2D3748), // Dark gray for readability
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
  }

  Widget _buildSegmentProgress(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Segment ${currentSegmentIndex + 1} of ${scene.narratorSegments.length}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: (currentSegmentIndex + 1) / scene.narratorSegments.length,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            theme.colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildEmotionCueDebug(ThemeData theme) {
    final currentSegment = scene.narratorSegments[currentSegmentIndex];
    
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.psychology,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 8),
          Text(
            'Emotion: ${currentSegment.emotionCue ?? 'neutral'}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:async';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/audio/unified_tts_service.dart';
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart';

/// Service for handling story narration with TTS and subtitle display
class StoryNarrationService {
  static final StoryNarrationService _instance = StoryNarrationService._internal();
  factory StoryNarrationService() => _instance;
  static StoryNarrationService get instance => _instance;
  StoryNarrationService._internal();

  static final Logger _logger = Logger();

  final TTSServiceInterface _ttsService = UnifiedTTSService.instance;
  
  // Narration state
  bool _isInitialized = false;
  bool _isNarrating = false;
  bool _isPaused = false;
  
  // Current narration
  List<String> _currentSentences = [];
  int _currentSentenceIndex = 0;
  
  // Settings
  double _speechRate = 0.5;
  double _speechVolume = 1.0;
  double _speechPitch = 1.0;
  
  // Stream controllers for UI updates
  final StreamController<String> _currentTextController = StreamController<String>.broadcast();
  final StreamController<NarrationProgress> _progressController = StreamController<NarrationProgress>.broadcast();
  final StreamController<bool> _narrationStateController = StreamController<bool>.broadcast();
  
  // Getters for streams
  Stream<String> get currentTextStream => _currentTextController.stream;
  Stream<NarrationProgress> get progressStream => _progressController.stream;
  Stream<bool> get narrationStateStream => _narrationStateController.stream;
  
  // Getters for settings
  double get speechRate => _speechRate;
  double get speechVolume => _speechVolume;
  double get speechPitch => _speechPitch;
  bool get isNarrating => _isNarrating;
  bool get isPaused => _isPaused;

  /// Initialize the TTS service
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.i('[StoryNarrationService] Service already initialized (Instance: $hashCode)');
      return;
    }

    try {
      _logger.i('[StoryNarrationService] Initializing TTS service (Instance: $hashCode)');

      // Request exclusive access for story narration
      if (!(_ttsService as UnifiedTTSService).requestAccess('story')) {
        throw Exception('Cannot access TTS service - already in use');
      }

      // Initialize the unified TTS service
      await _ttsService.initialize();

      // Set up TTS state monitoring
      _ttsService.stateStream.listen((state) {
        switch (state) {
          case TTSState.playing:
            _logger.d('[StoryNarrationService] TTS started (Instance: $hashCode)');
            _isNarrating = true;
            _narrationStateController.add(true);
            break;
          case TTSState.stopped:
            _logger.d('[StoryNarrationService] TTS completed (Instance: $hashCode)');
            _onSentenceCompleted();
            break;
          case TTSState.error:
            _logger.e('[StoryNarrationService] TTS error (Instance: $hashCode)');
            _isNarrating = false;
            _narrationStateController.add(false);
            break;
          case TTSState.paused:
            _logger.d('[StoryNarrationService] TTS paused (Instance: $hashCode)');
            break;
          default:
            break;
        }
      });

      // Set initial TTS settings
      await _updateTtsSettings();

      _isInitialized = true;
      _logger.i('[StoryNarrationService] TTS service initialized successfully (Instance: $hashCode)');

    } catch (e) {
      _logger.e('[StoryNarrationService] Failed to initialize TTS (Instance: $hashCode): $e');
      throw Exception('Failed to initialize story narration service: $e');
    }
  }

  /// Start narrating a scene (legacy support)
  Future<void> narrateScene(dynamic scene) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      _logger.i('[StoryNarrationService] Starting narration for scene');

      // Stop any current narration
      await stop();

      // Extract text based on scene type
      _currentSentences = [];

      // Handle different scene types
      if (scene is EnhancedSceneModel) {
        // Enhanced scene model
        final text = scene.text;
        if (text.isNotEmpty) {
          _currentSentences = _splitIntoSentences(text);
        }
      } else {
        // Legacy scene model - extract from narrator segments
        try {
          final narratorSegments = scene.narratorSegments as List<dynamic>?;
          if (narratorSegments != null) {
            for (final segment in narratorSegments) {
              final text = segment.text['en-US'] ?? '';
              if (text.isNotEmpty) {
                _currentSentences.add(text);
              }
            }
          }
        } catch (e) {
          _logger.w('[StoryNarrationService] Could not extract narrator segments: $e');
        }
      }

      if (_currentSentences.isEmpty) {
        _logger.w('[StoryNarrationService] No sentences to narrate in scene');
        return;
      }

      _currentSentenceIndex = 0;
      _updateProgress();

      // Start narrating the first sentence
      await _narrateCurrentSentence();

    } catch (e) {
      _logger.e('[StoryNarrationService] Failed to start scene narration: $e');
      _isNarrating = false;
      _narrationStateController.add(false);
    }
  }

  /// Split text into sentences for narration
  List<String> _splitIntoSentences(String text) {
    // Remove emotion cues in brackets
    final cleanText = text.replaceAll(RegExp(r'\[.*?\]'), '');

    // Split by sentence endings
    final sentences = cleanText
        .split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();

    return sentences;
  }

  /// Narrate the current sentence
  Future<void> _narrateCurrentSentence() async {
    if (_currentSentenceIndex >= _currentSentences.length) {
      _onNarrationCompleted();
      return;
    }

    final sentence = _currentSentences[_currentSentenceIndex];
    _logger.d('[StoryNarrationService] Narrating sentence ${_currentSentenceIndex + 1}/${_currentSentences.length}: $sentence');

    // Update current text for subtitle display
    _currentTextController.add(sentence);

    // Speak the sentence using unified TTS service
    await _ttsService.speakText(sentence);
  }

  /// Handle sentence completion
  void _onSentenceCompleted() {
    _currentSentenceIndex++;
    _updateProgress();
    
    if (_currentSentenceIndex < _currentSentences.length) {
      // Continue with next sentence after a brief pause
      Future.delayed(const Duration(milliseconds: 500), () {
        if (_isNarrating && !_isPaused) {
          _narrateCurrentSentence();
        }
      });
    } else {
      _onNarrationCompleted();
    }
  }

  /// Handle narration completion
  void _onNarrationCompleted() {
    _logger.i('[StoryNarrationService] Scene narration completed');
    _isNarrating = false;
    _narrationStateController.add(false);
    _currentTextController.add(''); // Clear subtitle
  }

  /// Update progress information
  void _updateProgress() {
    final progress = NarrationProgress(
      currentSentence: _currentSentenceIndex + 1,
      totalSentences: _currentSentences.length,
      progress: _currentSentences.isNotEmpty ? (_currentSentenceIndex + 1) / _currentSentences.length : 0.0,
    );
    _progressController.add(progress);
  }

  /// Pause narration
  Future<void> pause() async {
    if (_isNarrating && !_isPaused) {
      await _ttsService.pause();
      _isPaused = true;
      _logger.d('[StoryNarrationService] Narration paused');
    }
  }

  /// Resume narration
  Future<void> resume() async {
    if (_isPaused) {
      await _ttsService.resume();
      _isPaused = false;
      _logger.d('[StoryNarrationService] Narration resumed');
    }
  }

  /// Stop narration
  Future<void> stop() async {
    if (_isNarrating || _isPaused) {
      await _ttsService.stop();
      (_ttsService as UnifiedTTSService).releaseAccess('story');
      _isNarrating = false;
      _isPaused = false;
      _narrationStateController.add(false);
      _currentTextController.add(''); // Clear subtitle
      _logger.d('[StoryNarrationService] Narration stopped');
    }
  }

  /// Update speech rate (0.0 to 1.0)
  Future<void> setSpeechRate(double rate) async {
    _speechRate = rate.clamp(0.0, 1.0);
    await _updateTtsSettings();
    _logger.d('[StoryNarrationService] Speech rate updated to: $_speechRate');
  }

  /// Update speech volume (0.0 to 1.0)
  Future<void> setSpeechVolume(double volume) async {
    _speechVolume = volume.clamp(0.0, 1.0);
    await _updateTtsSettings();
    _logger.d('[StoryNarrationService] Speech volume updated to: $_speechVolume');
  }

  /// Update speech pitch (0.5 to 2.0)
  Future<void> setSpeechPitch(double pitch) async {
    _speechPitch = pitch.clamp(0.5, 2.0);
    await _updateTtsSettings();
    _logger.d('[StoryNarrationService] Speech pitch updated to: $_speechPitch');
  }

  /// Update TTS settings
  Future<void> _updateTtsSettings() async {
    final params = TTSSpeechParameters(
      rate: _speechRate,
      volume: _speechVolume,
      pitch: _speechPitch,
    );
    await _ttsService.setSpeechParameters(params);
    await _ttsService.setLanguage('en-US');
  }

  /// Dispose of resources
  void dispose() {
    _ttsService.stop();
    (_ttsService as UnifiedTTSService).releaseAccess('story');
    _currentTextController.close();
    _progressController.close();
    _narrationStateController.close();
    _logger.i('[StoryNarrationService] Service disposed');
  }
}

/// Progress information for narration
class NarrationProgress {
  final int currentSentence;
  final int totalSentences;
  final double progress;

  NarrationProgress({
    required this.currentSentence,
    required this.totalSentences,
    required this.progress,
  });
}

# Project Working Rules: Choice: Once Upon A Time (User & AI Agent)

**Preamble:**
This document outlines the working rules and guidelines for the development of the "Choice: Once Upon A Time" Flutter application. These rules apply to both the Human User/Developer and the AI Agent (Cursor) to ensure consistency, quality, security, and effective collaboration throughout the project lifecycle.

---

## I. Rules & Guidelines for AI Agent (Cursor)

**1. Primary Directive: Adherence to Project Documentation**
    * **TDD is King:** The **`docs/04_TECHNICAL_DESIGN_DOCUMENT.md`** [cite: 1-312] is your **PRIMARY BLUEPRINT**. All technical decisions, architectural choices, data models, and service implementations must strictly adhere to this document.
    * **Screen Specifications:** All UI implementation must follow the **`docs/02_SCREEN_SPECIFICATIONS.md`** document [cite: 446-683, 684-921] for screen structure, elements, user flows, and narrator synergy.
    * **Art Style:** All visual styling (colors, typography, UI element appearance) must conform to the **`docs/03_ART_STYLE_DEFINITION_AND_ASSET_PLAN.md`** [cite: 313-445].
    * **Sprint Plan:** Development tasks should follow the sequence and objectives outlined in the **`docs/05_MVP_DEVELOPMENT_SPRINTS.md`** document [cite: sprints.md].
    * **Story Content:** Data structures for stories must align with the provided **Story JSON files** (`assets/stories/story_01.json`, etc. [cite: story_01.json, story_02.json, story_03.json]).

**2. Code Generation & Modification Principles:**
    * **Analyze Existing Code:** Before generating new code or modifying existing files, thoroughly analyze the current project structure, existing widgets, services, providers, and data models to ensure seamless integration.
    * **Modular Design (Strict Adherence):**
        * Follow the feature-first folder structure (`lib/app/`, `lib/core/`, `lib/features/`, etc.) as defined in TDD Section 3.1 [cite: 53-56].
        * Create reusable widgets in `lib/shared_widgets/` and feature-specific widgets within their respective feature modules.
        * Maintain clear Separation of Concerns (UI, State Management (Riverpod), Business Logic, Data Models) as per TDD Section 3.1 & 3.3 [cite: 53-60, 74-83].
        * Apply the Single Responsibility Principle.
        * Utilize Riverpod for Dependency Injection.
    * **Responsiveness:** All UI must be responsive to various phone and tablet screen sizes and orientations.
    * **Testability:** Structure code to be easily testable. When adding new logic or significant widgets, create or suggest updates to corresponding unit and widget tests in the `test/` directory (TDD Section 10 [cite: 305-312]).
    * **Error Handling:** Implement robust and user-friendly error handling. Display clear UI messages for users and log detailed errors for developers. Avoid app crashes.
    * **Web Compatibility:** Ensure generated Flutter code remains web-compatible.
    * **Comments:** Add concise comments for complex logic, important decisions, or areas where TDD specifications are being directly implemented.
    * **Asset Handling:** Use `Image.asset()` with correct paths. Implement the "white background + `sceneId` text" placeholder for missing story scene images. For narrator audio, implement UI for text display and placeholder playback controls tied to the `TTSServiceInterface`.

**3. Security Guidelines (Strict Adherence):**
    * **NO Hardcoding of Secrets:** Absolutely no API keys, credentials, or sensitive strings directly in Dart code.
    * **Environment Variables:** All secrets **must** be handled via `flutter_dotenv` and a `.env` file (gitignored). Access via `dotenv.env['SECRET_NAME']`.
    * **`.gitignore`:** Respect the existing `.gitignore` file, especially for `.env`.
    * **Firebase Configuration:** Standard Firebase config files are managed as per project setup. Any *additional custom keys* for Firebase services must use environment variables.
    * **Backend Responsibility:** Delegate highly sensitive operations to secure backend services (Firebase Cloud Functions) as per TDD.

**4. Output & Deliverables:**
    * **Code Quality:** Generate clean, readable, well-commented, and efficient Flutter/Dart code adhering to effective Dart practices.
    * **Documentation Updates:** If changes necessitate updates to `README.md` or sprint completion reports (when completing a sprint's work), provide the updated text or indicate what needs updating.
    * **Explanation of Changes:** For complex modifications or new features, provide a brief explanation.
    * **Testing Notes:** If generating test files, ensure correct placement. If modifying logic, confirm existing tests should still pass or suggest updates.

**5. Task Execution:**
    * Focus on the specific task given, while adhering to all overarching project guidelines.
    * If a task is unclear or depends on unresolved issues, state this and ask for clarification.

---

## II. Rules & Guidelines for Human User/Developer

**1. Providing Clear Context & Instructions to AI Agent (Cursor):**
    * **Start New Sessions with Context:** Provide the **`docs/00_PROJECT_OVERVIEW_AND_ROADMAP_STATUS.md`** document.
    * **Reference Specific Documents:** For detailed tasks, explicitly tell Cursor which project documents to reference by their path (e.g., "Implement Screen 5 according to `docs/02_SCREEN_SPECIFICATIONS.md`...").
    * **Task Specificity:** Break down complex goals into smaller, well-defined tasks. Clearly state objectives, inputs, and desired outputs. Reference the current sprint and task number from `docs/05_MVP_DEVELOPMENT_SPRINTS.md`.
    * **Provide Code Snippets:** For debugging or modifications, provide relevant, concise code.

**2. Reviewing & Integrating AI Output:**
    * **Thorough Review:** **Always** review all code. Do not assume it is perfect.
    * **Verify Adherence:** Check against TDD, Art Style Guide, Screen Specs, modular design, and security guidelines.
    * **Test Rigorously:** Manually test features. Write/update unit, widget, and integration tests.
    * **Refactor if Necessary.**

**3. Iterative Development & AI as an Assistant:**
    * Treat Cursor as a pair programmer. Expect to iterate.
    * Use Cursor for brainstorming, boilerplate, or specific components, retaining human control over core architecture.

**4. Output & Deliverables:**
    * **Code Quality:** Generate clean, readable, well-commented (where necessary), and efficient Flutter/Dart code adhering to effective Dart practices.
    * **Documentation Updates & Status Reporting (NEW/ENHANCED):**
        * If your changes necessitate updates to `README.md` or if you are completing tasks defined within a specific sprint (from `docs/05_MVP_DEVELOPMENT_SPRINTS.md`), provide the updated text for `README.md` and generate a **Sprint Completion Report** (e.g., `sprint_XX_completion_report.md`).
        * **Crucially, in your Sprint Completion Report or at the end of any significant task completion, explicitly list the Task Numbers (e.g., "1.2", "2.5", "Sprint 1 - Task 3") from the main project roadmap located in `docs/00_PROJECT_OVERVIEW_AND_ROADMAP_STATUS.md` that your current work addresses.**
        * **For each listed Task Number, provide a brief self-assessment of its completion status from your perspective** (e.g., "Task 2.5: Core interactive prototype for story selection and one choice branch implemented as per specification.", "Task 3.1.1: Project setup and folder structure created.").
    * **Explanation of Changes:** For complex modifications or new features, provide a brief explanation of your approach and key changes made.
    * **Testing Notes:** If generating test files, ensure correct placement. If modifying logic, confirm existing relevant tests should still pass or suggest updates. Note any specific manual testing the user should perform on the generated code.


**5. Cloud Services & Manual Setup (e.g., Firebase):**
    * The human user is responsible for **manually applying configurations** in the Firebase console, creating the project, enabling services, and inputting initial data as per AI instructions.

**6. Version Control (Git):**
    * Commit changes frequently after reviewing AI contributions, with clear messages.

**7. Feedback Loop with AI:**
    * If Cursor's output is not as expected, provide specific, constructive feedback, referencing the exact rule or TDD specification it missed.

**By adhering to these mutual rules, both the Human User and the AI Agent (Cursor) can collaborate effectively.**
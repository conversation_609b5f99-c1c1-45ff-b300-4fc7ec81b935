import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart';

class TtsFullTestScreen extends ConsumerWidget {
  const TtsFullTestScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ttsService = ref.watch(ttsServiceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('TTS Full Test Screen'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Use these buttons to test the TTS service directly.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  print('[TTS Test] Speak button pressed.');
                  ttsService.speakText(
                    'This is a test of the text to speech service.',
                    emotionCue: 'neutral',
                  );
                },
                child: const Text('Speak Test Phrase'),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  print('[TTS Test] Stop button pressed.');
                  ttsService.stop();
                },
                child: const Text('Stop Speaking'),
              ),
              const SizedBox(height: 32),
              StreamBuilder<TTSState>(
                stream: ttsService.stateStream,
                builder: (context, snapshot) {
                  final state = snapshot.data ?? ttsService.getCurrentState();
                  return Text(
                    'Current TTS State: ${state.toString().split('.').last}',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.headlineSmall,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Enhanced narration widget with word-level highlighting and scene progression
class EnhancedNarrationWidget extends StatefulWidget {
  final String text;
  final String emotion;
  final StoryNarrationService narrationService;
  final StorySettingsService settingsService;
  final VoidCallback? onNarrationComplete;
  final bool autoStart;
  final Color? emotionColor;

  const EnhancedNarrationWidget({
    super.key,
    required this.text,
    required this.emotion,
    required this.narrationService,
    required this.settingsService,
    this.onNarrationComplete,
    this.autoStart = true,
    this.emotionColor,
  });

  @override
  State<EnhancedNarrationWidget> createState() => _EnhancedNarrationWidgetState();
}

class _EnhancedNarrationWidgetState extends State<EnhancedNarrationWidget>
    with TickerProviderStateMixin {
  late final AnimationController _progressController;
  late final AnimationController _pulseController;
  late final Animation<double> _progressAnimation;
  late final Animation<double> _pulseAnimation;

  List<String> _words = [];
  int _currentWordIndex = -1;
  bool _isNarrating = false;
  bool _isPaused = false;
  bool _hasCompleted = false;
  double _progress = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _prepareText();
    if (widget.autoStart) {
      WidgetsBinding.instance.addPostFrameCallback((_) => _startNarration());
    }
  }

  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  void _prepareText() {
    _words = widget.text
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .toList();
  }

  Future<void> _startNarration() async {
    if (_isNarrating || _hasCompleted) return;

    AppLogger.debug('[SCENE_DEBUG] Narrating: "${widget.text.substring(0, widget.text.length > 50 ? 50 : widget.text.length)}${widget.text.length > 50 ? '...' : ''}"');

    setState(() {
      _isNarrating = true;
      _isPaused = false;
      _currentWordIndex = -1;
      _progress = 0.0;
    });

    try {
      // Start pulse animation for visual feedback
      _pulseController.repeat(reverse: true);

      // Get narration settings
      final speed = widget.settingsService.narrationSpeed;
      final emotionCue = widget.emotion.isNotEmpty ? widget.emotion : 'neutral';

      AppLogger.debug('[SCENE_DEBUG] Narration settings - Speed: $speed, Emotion: $emotionCue');

      // Calculate timing for word highlighting
      final totalDuration = _calculateNarrationDuration(widget.text, speed);
      final wordDuration = Duration(
        milliseconds: (totalDuration.inMilliseconds / _words.length).round(),
      );

      // Start TTS narration with emotion-based settings
      await widget.narrationService.setSpeechRate(speed);
      await _speakTextWithEmotion(widget.text, emotionCue);

      // Simulate word-by-word highlighting
      for (int i = 0; i < _words.length; i++) {
        if (!_isNarrating) break; // Check if stopped

        setState(() {
          _currentWordIndex = i;
          _progress = (i + 1) / _words.length;
        });

        // Update progress animation
        _progressController.animateTo(_progress);

        // Wait for word duration (accounting for pauses)
        await _waitWithPauseSupport(wordDuration);
      }

      await _completeNarration();

    } catch (e) {
      AppLogger.error('[SCENE_DEBUG] Narration error', e);
      await _stopNarration();
    }
  }

  Future<void> _waitWithPauseSupport(Duration duration) async {
    final startTime = DateTime.now();
    while (DateTime.now().difference(startTime) < duration) {
      if (!_isNarrating) break;
      
      if (_isPaused) {
        await Future.delayed(const Duration(milliseconds: 100));
        continue;
      }
      
      await Future.delayed(const Duration(milliseconds: 50));
    }
  }

  Duration _calculateNarrationDuration(String text, double speed) {
    // Estimate duration based on text length and speed
    // Average reading speed: ~150 words per minute at normal speed
    final wordCount = text.split(RegExp(r'\s+')).length;
    final baseMinutes = wordCount / 150.0;
    final adjustedMinutes = baseMinutes / speed;
    return Duration(milliseconds: (adjustedMinutes * 60 * 1000).round());
  }

  Future<void> _speakTextWithEmotion(String text, String emotionCue) async {
    // Apply emotion-based voice parameters
    final emotionParams = _getEmotionParameters(emotionCue);

    await widget.narrationService.setSpeechRate(emotionParams['rate'] ?? 0.5);
    await widget.narrationService.setSpeechPitch(emotionParams['pitch'] ?? 1.0);
    await widget.narrationService.setSpeechVolume(emotionParams['volume'] ?? 1.0);

    // Use the narration service to speak the text
    // Create a mock scene for compatibility
    final mockScene = _MockEnhancedScene(text);
    await widget.narrationService.narrateScene(mockScene);
  }

  /// Get voice parameters based on emotion
  Map<String, double> _getEmotionParameters(String emotion) {
    switch (emotion.toLowerCase()) {
      case 'happy':
      case 'excited':
      case 'joyful':
        return {'rate': 0.6, 'pitch': 1.2, 'volume': 1.0};
      case 'sad':
      case 'disappointed':
        return {'rate': 0.4, 'pitch': 0.8, 'volume': 0.8};
      case 'angry':
      case 'frustrated':
        return {'rate': 0.7, 'pitch': 0.9, 'volume': 1.0};
      case 'surprised':
      case 'amazed':
        return {'rate': 0.5, 'pitch': 1.3, 'volume': 1.0};
      case 'calm':
      case 'peaceful':
        return {'rate': 0.4, 'pitch': 1.0, 'volume': 0.9};
      case 'mysterious':
      case 'suspenseful':
        return {'rate': 0.3, 'pitch': 0.9, 'volume': 0.8};
      default:
        return {'rate': 0.5, 'pitch': 1.0, 'volume': 1.0};
    }
  }

  Future<void> _completeNarration() async {
    AppLogger.debug('[SCENE_DEBUG] Narration completed successfully');

    setState(() {
      _isNarrating = false;
      _hasCompleted = true;
      _currentWordIndex = _words.length - 1;
      _progress = 1.0;
    });

    _pulseController.stop();
    _pulseController.reset();
    await _progressController.forward();

    widget.onNarrationComplete?.call();
  }

  Future<void> _pauseNarration() async {
    if (!_isNarrating) return;

    setState(() {
      _isPaused = true;
    });

    await widget.narrationService.pause();
    _pulseController.stop();
  }

  Future<void> _resumeNarration() async {
    if (!_isNarrating || !_isPaused) return;

    setState(() {
      _isPaused = false;
    });

    await widget.narrationService.resume();
    _pulseController.repeat(reverse: true);
  }

  Future<void> _stopNarration() async {
    setState(() {
      _isNarrating = false;
      _isPaused = false;
      _currentWordIndex = -1;
      _progress = 0.0;
    });

    await widget.narrationService.stop();
    _pulseController.stop();
    _pulseController.reset();
    _progressController.reset();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return StreamBuilder<bool>(
      stream: widget.settingsService.subtitlesEnabledStream,
      initialData: widget.settingsService.subtitlesEnabled,
      builder: (context, enabledSnapshot) {
        final enabled = enabledSnapshot.data ?? true;
        
        if (!enabled) return const SizedBox.shrink();
        
        return StreamBuilder<double>(
          stream: widget.settingsService.subtitleSizeStream,
          initialData: widget.settingsService.subtitleSize,
          builder: (context, sizeSnapshot) {
            final fontSize = sizeSnapshot.data ?? 18.0;
            
            return Column(
              children: [
                // Subtitle area with word highlighting
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _isNarrating && !_isPaused ? _pulseAnimation.value : 1.0,
                      child: Container(
                        margin: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 16.0 : 24.0,
                          vertical: 8.0,
                        ),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: (widget.emotionColor ?? theme.colorScheme.primary)
                                .withValues(alpha: 0.5),
                            width: 2,
                          ),
                        ),
                        child: _buildHighlightedText(fontSize),
                      ),
                    );
                  },
                ),

                // Progress indicator
                if (_isNarrating || _hasCompleted)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
                    child: Column(
                      children: [
                        AnimatedBuilder(
                          animation: _progressAnimation,
                          builder: (context, child) {
                            return LinearProgressIndicator(
                              value: _progressAnimation.value,
                              backgroundColor: Colors.grey[300],
                              valueColor: AlwaysStoppedAnimation<Color>(
                                widget.emotionColor ?? theme.colorScheme.primary,
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _isNarrating
                                  ? (_isPaused ? 'Paused' : 'Narrating...')
                                  : _hasCompleted
                                      ? 'Complete'
                                      : 'Ready',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            Text(
                              '${(_progress * 100).round()}%',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                // Control buttons
                if (_isNarrating || _hasCompleted)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_isNarrating) ...[
                          IconButton(
                            onPressed: _isPaused ? _resumeNarration : _pauseNarration,
                            icon: Icon(_isPaused ? Icons.play_arrow : Icons.pause),
                            tooltip: _isPaused ? 'Resume' : 'Pause',
                          ),
                          const SizedBox(width: 16),
                          IconButton(
                            onPressed: _stopNarration,
                            icon: const Icon(Icons.stop),
                            tooltip: 'Stop',
                          ),
                        ] else if (!_hasCompleted) ...[
                          IconButton(
                            onPressed: _startNarration,
                            icon: const Icon(Icons.play_arrow),
                            tooltip: 'Start Narration',
                          ),
                        ],
                      ],
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildHighlightedText(double fontSize) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        children: _words.asMap().entries.map((entry) {
          final index = entry.key;
          final word = entry.value;
          final isHighlighted = index == _currentWordIndex;
          final isCompleted = index < _currentWordIndex;

          return TextSpan(
            text: '$word ',
            style: TextStyle(
              color: isHighlighted
                  ? (widget.emotionColor ?? Colors.yellow)
                  : isCompleted
                      ? Colors.white.withValues(alpha: 0.9)
                      : Colors.white.withValues(alpha: 0.7),
              fontSize: fontSize,
              height: 1.4,
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.w500,
              backgroundColor: isHighlighted
                  ? (widget.emotionColor ?? Colors.yellow).withValues(alpha: 0.3)
                  : null,
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Mock scene class for compatibility with StoryNarrationService
class _MockEnhancedScene {
  final String text;

  _MockEnhancedScene(this.text);
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'dart:async';

/// Enhanced landscape story widget with stable full-screen images and cinema-like controls
/// Implements proper image preloading, word-level highlighting, and immersive storytelling experience
class EnhancedLandscapeStoryWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final EnhancedSceneModel scene;
  final Function(ChoiceOptionModel) onChoiceSelected;
  final VoidCallback? onSceneComplete;
  final StoryNarrationService narrationService;
  final StorySettingsService settingsService;

  const EnhancedLandscapeStoryWidget({
    super.key,
    required this.story,
    required this.scene,
    required this.onChoiceSelected,
    required this.narrationService,
    required this.settingsService,
    this.onSceneComplete,
  });

  @override
  State<EnhancedLandscapeStoryWidget> createState() => _EnhancedLandscapeStoryWidgetState();
}

class _EnhancedLandscapeStoryWidgetState extends State<EnhancedLandscapeStoryWidget>
    with TickerProviderStateMixin {

  late AnimationController _fadeController;
  late AnimationController _controlsController;

  // Image loading and caching for stability
  ImageProvider? _cachedImageProvider;
  bool _imageLoaded = false;
  bool _imagePreloading = false;
  final Completer<void> _imageLoadCompleter = Completer<void>();

  // Narration state with word-level tracking
  bool _isNarrating = false;
  bool _isPaused = false;
  bool _hasCompleted = false;
  int _currentSentenceIndex = 0;
  int _currentWordIndex = 0;
  List<String> _sentences = [];
  List<List<String>> _sentenceWords = [];

  // UI state and settings
  bool _showChoices = false;
  bool _showOverlayControls = false;
  bool _isInitialized = false;

  // Streams and subscriptions for proper cleanup
  StreamSubscription? _progressSubscription;
  StreamSubscription? _narrationStateSubscription;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInitialized) {
      _isInitialized = true;
      _initializeScene();
    }
  }

  void _initializeAnimations() {
    // Minimal animation controllers for disposal - no actual animations used
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _controlsController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    // Set controllers to completed state for immediate visibility
    _fadeController.value = 1.0;
    _controlsController.value = 1.0;

    AppLogger.debug('[SCENE_DEBUG] Animation controllers initialized - immediate visibility mode');
  }

  void _initializeScene() async {
    AppLogger.debug('[SCENE_DEBUG] Scene ${widget.scene.id} initialized - Image: ${widget.scene.getImagePath(widget.story.storyId)}');

    // Preload and cache the image for stability
    await _preloadImage();

    // Process text into sentences and words for highlighting
    _processTextForNarration();

    // Set up narration service listeners with proper cleanup
    _setupNarrationListeners();

    // Wait for image to be loaded, then ensure immediate visibility
    await _imageLoadCompleter.future;
    if (mounted) {
      AppLogger.debug('[SCENE_DEBUG] Image loaded, ensuring immediate visibility');

      // Force immediate visibility - no animations
      _fadeController.value = 1.0;
      _controlsController.value = 1.0;

      // Trigger a rebuild to ensure UI is visible
      setState(() {
        // Force rebuild with loaded state
      });

      AppLogger.debug('[SCENE_DEBUG] UI components should now be visible');

      // Start narration immediately after ensuring visibility
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted && _imageLoaded) {
          AppLogger.debug('[SCENE_DEBUG] Starting narration');
          _startNarration();
        }
      });
    }
  }

  Future<void> _preloadImage() async {
    if (_imagePreloading || _imageLoaded) {
      AppLogger.debug('[SCENE_DEBUG] Image preloading skipped - already loading: $_imagePreloading, loaded: $_imageLoaded');
      return;
    }

    _imagePreloading = true;
    final imagePath = widget.scene.getImagePath(widget.story.storyId);
    _cachedImageProvider = AssetImage(imagePath);

    AppLogger.debug('[SCENE_DEBUG] Starting image preload: $imagePath');

    try {
      // Preload the image and wait for completion
      await precacheImage(_cachedImageProvider!, context);

      if (mounted) {
        AppLogger.debug('[SCENE_DEBUG] Image precached successfully, updating state');

        setState(() {
          _imageLoaded = true;
        });

        if (!_imageLoadCompleter.isCompleted) {
          _imageLoadCompleter.complete();
          AppLogger.debug('[SCENE_DEBUG] Image load completer completed');
        }

        AppLogger.debug('[SCENE_DEBUG] Image successfully preloaded and cached - _imageLoaded: $_imageLoaded');
      }
    } catch (error) {
      AppLogger.error('[SCENE_DEBUG] Failed to preload image: $imagePath', error);

      // Complete anyway to prevent hanging
      if (!_imageLoadCompleter.isCompleted) {
        _imageLoadCompleter.complete();
        AppLogger.debug('[SCENE_DEBUG] Image load completer completed (with error)');
      }
    } finally {
      _imagePreloading = false;
      AppLogger.debug('[SCENE_DEBUG] Image preloading process finished');
    }
  }

  void _processTextForNarration() {
    // Split text into sentences for progress tracking
    _sentences = _splitIntoSentences(widget.scene.text);

    // Split each sentence into words for highlighting
    _sentenceWords = _sentences.map((sentence) {
      return sentence.split(RegExp(r'\s+'))
          .where((word) => word.isNotEmpty)
          .toList();
    }).toList();

    AppLogger.debug('[SCENE_DEBUG] Processed ${_sentences.length} sentences with ${_sentenceWords.fold(0, (sum, words) => sum + words.length)} total words');
  }

  void _setupNarrationListeners() {
    // Clean up existing subscriptions
    _progressSubscription?.cancel();
    _narrationStateSubscription?.cancel();

    // Listen to narration service progress with word-level tracking
    _progressSubscription = widget.narrationService.progressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _currentSentenceIndex = (progress.currentSentence - 1).clamp(0, _sentences.length - 1);
          // Update current word index based on progress
          _updateWordHighlighting();
        });
      }
    });

    // Listen to narration state changes
    _narrationStateSubscription = widget.narrationService.narrationStateStream.listen((isNarrating) {
      if (mounted) {
        setState(() {
          _isNarrating = isNarrating;
          if (!isNarrating) {
            _hasCompleted = true;
            _currentWordIndex = 0; // Reset word highlighting
          }
        });
      }
    });
  }

  void _updateWordHighlighting() {
    // Update the current word index for highlighting
    // This is a simplified version - in a real implementation, you'd sync with TTS word boundaries
    if (_currentSentenceIndex < _sentenceWords.length) {
      final wordsInCurrentSentence = _sentenceWords[_currentSentenceIndex].length;
      _currentWordIndex = (_currentWordIndex + 1) % (wordsInCurrentSentence + 1);
    }
  }



  List<String> _splitIntoSentences(String text) {
    // Split by sentence-ending punctuation
    final sentences = text.split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
    
    return sentences.isNotEmpty ? sentences : [text];
  }

  Future<void> _startNarration() async {
    if (_isNarrating || _hasCompleted) return;

    setState(() {
      _isNarrating = true;
      _isPaused = false;
      _currentSentenceIndex = 0;
    });

    AppLogger.debug('[SCENE_DEBUG] Narrating: "${widget.scene.text.substring(0, widget.scene.text.length > 50 ? 50 : widget.scene.text.length)}${widget.scene.text.length > 50 ? '...' : ''}"');

    try {
      // Start narration with sentence-level progress tracking
      await _narrateWithProgress();
      
      await _completeNarration();
    } catch (e) {
      AppLogger.error('[SCENE_DEBUG] Narration error', e);
      await _stopNarration();
    }
  }

  Future<void> _narrateWithProgress() async {
    // Use the scene narration service which handles sentence-level progress internally
    await widget.narrationService.narrateScene(widget.scene);
    
  }

  Future<void> _pauseNarration() async {
    if (_isNarrating && !_isPaused) {
      setState(() {
        _isPaused = true;
      });
      await widget.narrationService.pause();
      AppLogger.debug('[SCENE_DEBUG] Narration paused');
    }
  }

  Future<void> _resumeNarration() async {
    if (_isNarrating && _isPaused) {
      setState(() {
        _isPaused = false;
      });
      await widget.narrationService.resume();
      AppLogger.debug('[SCENE_DEBUG] Narration resumed');
    }
  }

  Future<void> _stopNarration() async {
    setState(() {
      _isNarrating = false;
      _isPaused = false;
    });
    await widget.narrationService.stop();
    AppLogger.debug('[SCENE_DEBUG] Narration stopped');
  }

  Future<void> _completeNarration() async {
    AppLogger.debug('[SCENE_DEBUG] Narration completed successfully');

    setState(() {
      _isNarrating = false;
      _hasCompleted = true;
      _currentSentenceIndex = _sentences.length - 1;
      _currentWordIndex = 0; // Reset word highlighting
    });

    // Show choices or next scene button
    if (widget.scene.choices?.isNotEmpty ?? false) {
      setState(() {
        _showChoices = true;
      });
    } else {
      // Auto-advance if enabled in settings
      final autoPlay = widget.settingsService.autoPlay;
      if (autoPlay) {
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          widget.onSceneComplete?.call();
        }
      }
    }
  }

  /// Navigate to Calm Exit Screen with story context
  Future<void> _navigateToCalmExitScreen(BuildContext context) async {
    try {
      AppLogger.debug('[SCENE_DEBUG] Navigating to Calm Exit Screen with story context');

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Navigate to Calm Exit Screen using GoRouter (before async operations)
      // The Calm Exit Screen will handle the gentle transition and provide options
      // to continue the story or exit to home
      context.push('/calm_exit');

      // Pause any ongoing narration after navigation
      if (_isNarrating && !_isPaused) {
        await _pauseNarration();
      }

      AppLogger.debug('[SCENE_DEBUG] Navigation to Calm Exit Screen initiated');
    } catch (e) {
      AppLogger.error('[SCENE_DEBUG] Error navigating to Calm Exit Screen', e);
      // Fallback: just pause narration if navigation fails
      if (_isNarrating && !_isPaused) {
        await _pauseNarration();
      }
    }
  }

  @override
  void dispose() {
    // Clean up subscriptions
    _progressSubscription?.cancel();
    _narrationStateSubscription?.cancel();

    // Dispose animation controllers
    _fadeController.dispose();
    _controlsController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AppLogger.debug('[SCENE_DEBUG] Build called - _imageLoaded: $_imageLoaded, _isInitialized: $_isInitialized');

    // Full-screen layout without app bar for cinema-like experience
    // PopScope handles device back button to navigate to Calm Exit Screen
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) return;

        AppLogger.debug('[SCENE_DEBUG] Device back button pressed - navigating to Calm Exit Screen');

        // Navigate to Calm Exit Screen with story context
        await _navigateToCalmExitScreen(context);
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: _imageLoaded ? _buildMainContent() : _buildLoadingScreen(),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    AppLogger.debug('[SCENE_DEBUG] Rendering loading screen');
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 16),
            Text(
              'Loading scene...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    AppLogger.debug('[SCENE_DEBUG] Rendering main content - image should be visible');
    return Stack(
      children: [
        // Full-screen stable background image
        _buildStableFullScreenImage(),

        // Floating settings button (top-right) - always visible
        _buildFloatingSettingsButton(),

        // Control interface overlay (bottom) - always visible
        _buildControlInterfaceOverlay(),

        // Choice selection overlay
        if (_showChoices) _buildChoiceOverlay(),
      ],
    );
  }

  Widget _buildStableFullScreenImage() {
    AppLogger.debug('[SCENE_DEBUG] Building stable image - _cachedImageProvider: ${_cachedImageProvider != null ? 'loaded' : 'null'}');

    // Remove FadeTransition to prevent image flashing - direct container for stability
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        image: _cachedImageProvider != null
            ? DecorationImage(
                image: _cachedImageProvider!,
                fit: BoxFit.cover,
              )
            : null,
        color: Colors.black, // Fallback background
      ),
      // No child widget to ensure image stability
      // Image remains visible throughout entire scene duration
    );
  }

  Widget _buildFloatingSettingsButton() {
    AppLogger.debug('[SCENE_DEBUG] Building floating settings button - should be visible');

    return Positioned(
      top: 16,
      right: 16,
      child: SafeArea(
        child: Column(
          children: [
            // Floating settings button - always visible, no animations
            Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: () {
                  AppLogger.debug('[SCENE_DEBUG] Settings button pressed');
                  setState(() {
                    _showOverlayControls = !_showOverlayControls;
                  });
                },
                icon: const Icon(Icons.settings),
                color: Colors.white,
                iconSize: 24,
              ),
            ),

            // Settings panel
            if (_showOverlayControls) _buildSettingsPanel(),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsPanel() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Text Font Size slider (persistent)
          StreamBuilder<double>(
            stream: widget.settingsService.textFontSizeStream,
            initialData: widget.settingsService.textFontSize,
            builder: (context, snapshot) {
              final fontSize = snapshot.data ?? 18.0;
              return _buildSliderControl(
                'Text Font Size',
                fontSize,
                12.0,
                32.0,
                widget.settingsService.setTextFontSize,
              );
            },
          ),

          const SizedBox(height: 12),

          // Control Row Background Transparency slider (persistent)
          StreamBuilder<double>(
            stream: widget.settingsService.controlRowTransparencyStream,
            initialData: widget.settingsService.controlRowTransparency,
            builder: (context, snapshot) {
              final transparency = snapshot.data ?? 0.5;
              return _buildSliderControl(
                'Control Background Transparency',
                transparency * 100,
                0.0,
                100.0,
                (value) => widget.settingsService.setControlRowTransparency(value / 100),
              );
            },
          ),

          const SizedBox(height: 12),

          // Narration speed slider
          StreamBuilder<double>(
            stream: widget.settingsService.narrationSpeedStream,
            initialData: widget.settingsService.narrationSpeed,
            builder: (context, snapshot) {
              final speed = snapshot.data ?? 0.5;
              return _buildSliderControl(
                'Narration Speed',
                speed * 100,
                10.0,
                100.0,
                (value) => widget.settingsService.setNarrationSpeed(value / 100),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSliderControl(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ${value.round()}',
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
        SizedBox(
          width: 150,
          child: Slider(
            value: value.clamp(min, max),
            min: min,
            max: max,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildControlInterfaceOverlay() {
    AppLogger.debug('[SCENE_DEBUG] Building control interface overlay - should be visible');

    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: StreamBuilder<double>(
          stream: widget.settingsService.controlRowTransparencyStream,
          initialData: widget.settingsService.controlRowTransparency,
          builder: (context, snapshot) {
            final transparency = snapshot.data ?? 0.5;
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withValues(alpha: transparency),
                    Colors.transparent,
                  ],
                ),
              ),
              child: _buildHorizontalControlRow(),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHorizontalControlRow() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Column 1 (Left): Play/Pause/Replay button
          _buildPlayControlColumn(),

          const SizedBox(width: 12),

          // Column 2 (Center): Current story text/subtitle display (flexible width)
          Expanded(
            child: _buildTextDisplayColumn(),
          ),

          const SizedBox(width: 12),

          // Column 3 (Right): Next scene button OR choice selection button
          _buildNavigationColumn(),
        ],
      ),
    );
  }

  Widget _buildPlayControlColumn() {
    IconData iconData;
    VoidCallback? onPressed;
    Color backgroundColor;
    Color foregroundColor;

    if (_hasCompleted) {
      // Replay button
      iconData = Icons.replay;
      onPressed = () {
        setState(() {
          _hasCompleted = false;
          _currentSentenceIndex = 0;
          _currentWordIndex = 0;
        });
        _startNarration();
      };
      backgroundColor = Colors.green.withValues(alpha: 0.9);
      foregroundColor = Colors.white;
    } else if (_isNarrating) {
      // Pause/Resume button
      iconData = _isPaused ? Icons.play_arrow : Icons.pause;
      onPressed = _isPaused ? _resumeNarration : _pauseNarration;
      backgroundColor = Colors.blue.withValues(alpha: 0.9);
      foregroundColor = Colors.white;
    } else {
      // Play button
      iconData = Icons.play_arrow;
      onPressed = _startNarration;
      backgroundColor = Colors.white.withValues(alpha: 0.9);
      foregroundColor = Colors.blue[800]!;
    }

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(iconData, size: 28),
        color: foregroundColor,
        padding: const EdgeInsets.all(12),
      ),
    );
  }

  Widget _buildTextDisplayColumn() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: StreamBuilder<double>(
        stream: widget.settingsService.textFontSizeStream,
        initialData: widget.settingsService.textFontSize,
        builder: (context, snapshot) {
          final fontSize = snapshot.data ?? 18.0;
          return _buildHighlightedText(fontSize);
        },
      ),
    );
  }

  Widget _buildHighlightedText(double fontSize) {
    if (_currentSentenceIndex >= _sentences.length) {
      return Text(
        widget.scene.text,
        style: TextStyle(
          fontSize: fontSize,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
        maxLines: 3,
        overflow: TextOverflow.ellipsis,
      );
    }

    final words = _sentenceWords[_currentSentenceIndex];

    // Build text with word highlighting
    return RichText(
      textAlign: TextAlign.center,
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        children: words.asMap().entries.map((entry) {
          final index = entry.key;
          final word = entry.value;
          final isCurrentWord = _isNarrating && index == _currentWordIndex;

          return TextSpan(
            text: '$word ',
            style: TextStyle(
              fontSize: fontSize,
              color: isCurrentWord ? Colors.amber : Colors.white,
              fontWeight: isCurrentWord ? FontWeight.bold : FontWeight.w500,
              backgroundColor: isCurrentWord
                  ? Colors.amber.withValues(alpha: 0.3)
                  : null,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildNavigationColumn() {
    final hasChoices = widget.scene.choices?.isNotEmpty ?? false;
    final canAdvance = _hasCompleted && !hasChoices;
    final canShowChoices = hasChoices && _hasCompleted;

    IconData iconData;
    VoidCallback? onPressed;
    Color backgroundColor;
    String tooltip;

    if (canAdvance) {
      iconData = Icons.arrow_forward;
      onPressed = widget.onSceneComplete;
      backgroundColor = Colors.green.withValues(alpha: 0.9);
      tooltip = 'Next Scene';
    } else if (canShowChoices) {
      iconData = Icons.touch_app;
      onPressed = () {
        setState(() {
          _showChoices = true;
        });
      };
      backgroundColor = Colors.orange.withValues(alpha: 0.9);
      tooltip = 'Make Choice';
    } else {
      iconData = Icons.hourglass_empty;
      onPressed = null;
      backgroundColor = Colors.grey.withValues(alpha: 0.5);
      tooltip = 'Wait for narration';
    }

    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(24),
          boxShadow: onPressed != null ? [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(iconData, size: 28),
          color: Colors.white,
          padding: const EdgeInsets.all(12),
        ),
      ),
    );
  }



  Widget _buildChoiceOverlay() {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'What should happen next?',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              ...widget.scene.choices?.map((choice) => _buildChoiceButton(choice)) ?? [],

              const SizedBox(height: 16),

              TextButton(
                onPressed: () {
                  setState(() {
                    _showChoices = false;
                  });
                },
                child: const Text('Cancel'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChoiceButton(ChoiceOptionModel choice) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 12),
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _showChoices = false;
          });
          widget.onChoiceSelected(choice);
        },
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.all(16),
          backgroundColor: Colors.blue[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          choice.option,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

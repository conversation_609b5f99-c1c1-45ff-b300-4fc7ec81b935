import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/screens/tts_full_test_screen.dart'; // Your actual path to the test screen

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'TTS Service Full Test',
      theme: ThemeData(
        primarySwatch: Colors.deepPurple,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const TtsFullTestScreen(), // Set the test screen as the home
      debugShowCheckedModeBanner: false,
    );
  }
}
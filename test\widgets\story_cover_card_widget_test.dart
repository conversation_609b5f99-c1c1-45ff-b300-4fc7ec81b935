import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/story_cover_card_widget.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

void main() {
  group('StoryCoverCardWidget', () {
    testWidgets('should display story information correctly', (WidgetTester tester) async {
      // Arrange
      const story = StoryMetadataModel(
        id: 'test_story',
        title: {'en-US': 'Test Story Title'},
        coverImageUrl: 'test_cover.png',
        loglineShort: {'en-US': 'Test logline'},
        targetMoralValue: 'Honesty',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        estimatedDurationMinutes: 8,
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StoryCoverCardWidget(
              story: story,
              languageCode: 'en-US',
              isOneColumn: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Test Story Title'), findsOneWidget);
      expect(find.text('Honesty'), findsOneWidget);
      expect(find.text('8 min'), findsOneWidget);
      expect(find.text('Ages 4-6'), findsOneWidget);
      expect(find.byIcon(Icons.access_time), findsOneWidget);
    });

    testWidgets('should display NEW badge when story is new', (WidgetTester tester) async {
      // Arrange
      const story = StoryMetadataModel(
        id: 'new_story',
        title: {'en-US': 'New Story'},
        coverImageUrl: 'new_cover.png',
        loglineShort: {'en-US': 'New story logline'},
        targetMoralValue: 'Kindness',
        version: '1.0.0',
        isNew: true,
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StoryCoverCardWidget(
              story: story,
              languageCode: 'en-US',
              isOneColumn: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('NEW'), findsOneWidget);
    });

    testWidgets('should display LOCKED badge and lock icon when story is locked', (WidgetTester tester) async {
      // Arrange
      const story = StoryMetadataModel(
        id: 'locked_story',
        title: {'en-US': 'Locked Story'},
        coverImageUrl: 'locked_cover.png',
        loglineShort: {'en-US': 'Locked story logline'},
        targetMoralValue: 'Perseverance',
        version: '1.0.0',
        isLocked: true,
        isFree: false,
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '5-7',
        initialSceneId: 'scene_01',
      );

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StoryCoverCardWidget(
              story: story,
              languageCode: 'en-US',
              isOneColumn: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('LOCKED'), findsOneWidget);
      expect(find.byIcon(Icons.lock), findsOneWidget);
    });

    testWidgets('should call onTap when tapped', (WidgetTester tester) async {
      // Arrange
      const story = StoryMetadataModel(
        id: 'tappable_story',
        title: {'en-US': 'Tappable Story'},
        coverImageUrl: 'tappable_cover.png',
        loglineShort: {'en-US': 'Tappable story logline'},
        targetMoralValue: 'Empathy',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      bool wasTapped = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryCoverCardWidget(
              story: story,
              languageCode: 'en-US',
              isOneColumn: false,
              onTap: () => wasTapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(StoryCoverCardWidget));
      await tester.pump();

      // Assert
      expect(wasTapped, true);
    });

    testWidgets('should display localized content for different languages', (WidgetTester tester) async {
      // Arrange
      const story = StoryMetadataModel(
        id: 'multilingual_story',
        title: {
          'en-US': 'English Title',
          'es-ES': 'Título en Español'
        },
        coverImageUrl: 'multilingual_cover.png',
        loglineShort: {
          'en-US': 'English logline',
          'es-ES': 'Logline en español'
        },
        targetMoralValue: 'Honesty',
        version: '1.0.0',
        supportedLanguages: ['en-US', 'es-ES'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      // Act - Test English
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StoryCoverCardWidget(
              story: story,
              languageCode: 'en-US',
              isOneColumn: false,
            ),
          ),
        ),
      );

      // Assert - English content
      expect(find.text('English Title'), findsOneWidget);
      expect(find.text('Título en Español'), findsNothing);

      // Act - Test Spanish
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StoryCoverCardWidget(
              story: story,
              languageCode: 'es-ES',
              isOneColumn: false,
            ),
          ),
        ),
      );

      // Assert - Spanish content
      expect(find.text('Título en Español'), findsOneWidget);
      expect(find.text('English Title'), findsNothing);
    });

    testWidgets('should handle long titles with ellipsis', (WidgetTester tester) async {
      // Arrange
      const story = StoryMetadataModel(
        id: 'long_title_story',
        title: {'en-US': 'This is a very long story title that should be truncated with ellipsis when displayed'},
        coverImageUrl: 'long_title_cover.png',
        loglineShort: {'en-US': 'Long title story logline'},
        targetMoralValue: 'Patience',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 200, // Constrain width to force truncation
              child: StoryCoverCardWidget(
                story: story,
                languageCode: 'en-US',
                isOneColumn: false,
              ),
            ),
          ),
        ),
      );

      // Assert
      final titleWidget = tester.widget<Text>(
        find.text('This is a very long story title that should be truncated with ellipsis when displayed'),
      );
      expect(titleWidget.overflow, TextOverflow.ellipsis);
      expect(titleWidget.maxLines, 2);
    });

    testWidgets('should display placeholder image when cover image is not available', (WidgetTester tester) async {
      // Arrange
      const story = StoryMetadataModel(
        id: 'placeholder_story',
        title: {'en-US': 'Placeholder Story'},
        coverImageUrl: 'assets/images/nonexistent_cover.png',
        loglineShort: {'en-US': 'Placeholder story logline'},
        targetMoralValue: 'Courage',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StoryCoverCardWidget(
              story: story,
              languageCode: 'en-US',
            ),
          ),
        ),
      );

      // Assert - Should show placeholder with book icon
      expect(find.byIcon(Icons.book), findsOneWidget);
      expect(find.text('nonexistent_cover.png'), findsOneWidget);
    });
  });
}

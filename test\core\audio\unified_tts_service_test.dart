import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/core/audio/unified_tts_service.dart';
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart';
import 'package:choice_once_upon_a_time/models/text_segment_model.dart';

void main() {
  group('UnifiedTTSService', () {
    late UnifiedTTSService ttsService;

    setUp(() {
      ttsService = UnifiedTTSService.instance;
    });

    tearDown(() async {
      await ttsService.stop();
      ttsService.releaseAccess('test');
    });

    test('should be a singleton', () {
      final instance1 = UnifiedTTSService.instance;
      final instance2 = UnifiedTTSService();
      expect(instance1, same(instance2));
    });

    test('should implement TTSServiceInterface', () {
      expect(ttsService, isA<TTSServiceInterface>());
    });

    test('should initialize successfully', () async {
      final result = await ttsService.initialize();
      expect(result, isTrue);
      expect(ttsService.isAvailable, isTrue);
    });

    test('should handle multiple initialization calls', () async {
      final result1 = await ttsService.initialize();
      final result2 = await ttsService.initialize();
      expect(result1, isTrue);
      expect(result2, isTrue);
    });

    group('Access Control', () {
      test('should grant access to first requester', () {
        final granted = ttsService.requestAccess('story');
        expect(granted, isTrue);
      });

      test('should deny access to second requester', () {
        ttsService.requestAccess('story');
        final denied = ttsService.requestAccess('voice_guide');
        expect(denied, isFalse);
      });

      test('should allow same user to request access multiple times', () {
        ttsService.requestAccess('story');
        final granted = ttsService.requestAccess('story');
        expect(granted, isTrue);
      });

      test('should release access properly', () {
        ttsService.requestAccess('story');
        ttsService.releaseAccess('story');
        final granted = ttsService.requestAccess('voice_guide');
        expect(granted, isTrue);
      });

      test('should not release access for wrong user', () {
        ttsService.requestAccess('story');
        ttsService.releaseAccess('voice_guide');
        final denied = ttsService.requestAccess('voice_guide');
        expect(denied, isFalse);
      });
    });

    group('TTS Operations', () {
      setUp(() async {
        await ttsService.initialize();
        ttsService.requestAccess('test');
      });

      test('should set language successfully', () async {
        final result = await ttsService.setLanguage('en-US');
        expect(result, isTrue);
      });

      test('should set speech parameters', () async {
        const params = TTSSpeechParameters(
          rate: 0.5,
          pitch: 1.0,
          volume: 0.8,
        );
        
        // Should not throw
        await ttsService.setSpeechParameters(params);
      });

      test('should speak text', () async {
        final result = await ttsService.speakText('Hello world');
        expect(result, isTrue);
      });

      test('should speak text with emotion', () async {
        final result = await ttsService.speakText('Hello world', emotionCue: 'happy');
        expect(result, isTrue);
      });

      test('should speak screen introduction', () async {
        final result = await ttsService.speakScreenIntroduction(
          text: 'Welcome to the story',
          emotionCue: 'gentle',
        );
        expect(result, isTrue);
      });

      test('should handle pause and resume', () async {
        await ttsService.speakText('Long text for testing pause and resume functionality');
        
        // Should not throw
        await ttsService.pause();
        expect(ttsService.isPaused, isTrue);
        
        await ttsService.resume();
      });

      test('should handle stop', () async {
        await ttsService.speakText('Text to be stopped');
        
        // Should not throw
        await ttsService.stop();
        expect(ttsService.isSpeaking, isFalse);
      });
    });

    group('State Management', () {
      test('should track current state', () {
        expect(ttsService.getCurrentState(), isA<TTSState>());
      });

      test('should provide state stream', () {
        expect(ttsService.stateStream, isA<Stream<TTSState>>());
      });

      test('should provide progress stream', () {
        expect(ttsService.progressStream, isA<Stream<double>>());
      });

      test('should provide word boundary stream', () {
        expect(ttsService.wordBoundaryStream, isA<Stream<String>>());
      });
    });

    group('Error Handling', () {
      test('should handle invalid language gracefully', () async {
        final result = await ttsService.setLanguage('invalid-lang');
        // Should not throw, may return false
        expect(result, isA<bool>());
      });

      test('should handle empty text', () async {
        final result = await ttsService.speakText('');
        expect(result, isA<bool>());
      });
    });

    group('Conflict Prevention', () {
      test('should prevent simultaneous usage by different users', () async {
        await ttsService.initialize();
        
        // Story service gets access
        expect(ttsService.requestAccess('story'), isTrue);
        
        // Voice guide should be denied
        expect(ttsService.requestAccess('voice_guide'), isFalse);
        
        // Story can speak
        final storyResult = await ttsService.speakText('Story narration');
        expect(storyResult, isTrue);
        
        // Release story access
        ttsService.releaseAccess('story');
        
        // Now voice guide can get access
        expect(ttsService.requestAccess('voice_guide'), isTrue);
        
        final voiceResult = await ttsService.speakText('Voice guide');
        expect(voiceResult, isTrue);
      });

      test('should handle force stop', () async {
        await ttsService.initialize();
        ttsService.requestAccess('story');
        
        await ttsService.speakText('Text being spoken');
        
        // Force stop should work regardless of user
        await ttsService.forceStop();
        
        // Should be able to request access again
        expect(ttsService.requestAccess('voice_guide'), isTrue);
      });
    });

    group('Disposal', () {
      test('should dispose properly', () async {
        await ttsService.initialize();
        ttsService.requestAccess('test');
        
        // Should not throw
        await ttsService.dispose();
        
        expect(ttsService.isAvailable, isFalse);
      });
    });
  });
}

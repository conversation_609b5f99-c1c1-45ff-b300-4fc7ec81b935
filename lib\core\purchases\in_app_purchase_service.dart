import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for handling in-app purchases
class InAppPurchaseService {
  static const String _logPrefix = 'InAppPurchase';
  
  // Product IDs - these should match your App Store Connect / Google Play Console setup
  static const String premiumMonthlyId = 'premium_monthly';
  static const String premiumYearlyId = 'premium_yearly';
  static const String unlockAllStoriesId = 'unlock_all_stories';
  
  static const Set<String> _productIds = {
    premiumMonthlyId,
    premiumYearlyId,
    unlockAllStoriesId,
  };

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  List<ProductDetails> _products = [];
  final List<PurchaseDetails> _purchases = [];
  bool _isAvailable = false;
  bool _purchasePending = false;
  String? _queryProductError;

  // Stream controllers for state management
  final _purchaseStateController = StreamController<PurchaseState>.broadcast();
  final _productsController = StreamController<List<ProductDetails>>.broadcast();

  Stream<PurchaseState> get purchaseStateStream => _purchaseStateController.stream;
  Stream<List<ProductDetails>> get productsStream => _productsController.stream;

  List<ProductDetails> get products => _products;
  bool get isAvailable => _isAvailable;
  bool get purchasePending => _purchasePending;
  String? get queryProductError => _queryProductError;

  /// Initialize the purchase service
  Future<void> initialize() async {
    try {
      AppLogger.info('$_logPrefix: Initializing in-app purchase service');
      
      _isAvailable = await _inAppPurchase.isAvailable();
      if (!_isAvailable) {
        AppLogger.warning('$_logPrefix: In-app purchases not available on this device');
        return;
      }

      // Listen to purchase updates
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => AppLogger.debug('$_logPrefix: Purchase stream closed'),
        onError: (error) => AppLogger.error('$_logPrefix: Purchase stream error', error),
      );

      // Load products
      await loadProducts();
      
      // Restore previous purchases
      await restorePurchases();
      
      AppLogger.info('$_logPrefix: Service initialized successfully');
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to initialize service', e, stackTrace);
    }
  }

  /// Load available products from the store
  Future<void> loadProducts() async {
    try {
      AppLogger.debug('$_logPrefix: Loading products');
      
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_productIds);
      
      if (response.notFoundIDs.isNotEmpty) {
        AppLogger.warning('$_logPrefix: Products not found: ${response.notFoundIDs}');
      }
      
      if (response.error != null) {
        _queryProductError = response.error!.message;
        AppLogger.error('$_logPrefix: Error loading products: ${response.error!.message}');
        return;
      }

      _products = response.productDetails;
      _productsController.add(_products);
      
      AppLogger.info('$_logPrefix: Loaded ${_products.length} products');
      for (final product in _products) {
        AppLogger.debug('$_logPrefix: Product: ${product.id} - ${product.title} - ${product.price}');
      }
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error loading products', e, stackTrace);
      _queryProductError = e.toString();
    }
  }

  /// Purchase a product
  Future<bool> purchaseProduct(ProductDetails product) async {
    try {
      AppLogger.info('$_logPrefix: Initiating purchase for ${product.id}');
      
      if (!_isAvailable) {
        AppLogger.error('$_logPrefix: In-app purchases not available');
        return false;
      }

      _purchasePending = true;
      _purchaseStateController.add(PurchaseState.purchasing);

      final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
      
      bool success;
      if (product.id == unlockAllStoriesId) {
        // Non-consumable product
        success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      } else {
        // Subscription products
        success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      }

      if (!success) {
        _purchasePending = false;
        _purchaseStateController.add(PurchaseState.error);
        AppLogger.error('$_logPrefix: Failed to initiate purchase for ${product.id}');
      }

      return success;
    } catch (e, stackTrace) {
      _purchasePending = false;
      _purchaseStateController.add(PurchaseState.error);
      AppLogger.error('$_logPrefix: Error purchasing product ${product.id}', e, stackTrace);
      return false;
    }
  }

  /// Restore previous purchases
  Future<void> restorePurchases() async {
    try {
      AppLogger.info('$_logPrefix: Restoring purchases');
      
      if (!_isAvailable) {
        AppLogger.error('$_logPrefix: In-app purchases not available');
        return;
      }

      await _inAppPurchase.restorePurchases();
      AppLogger.info('$_logPrefix: Purchase restoration completed');
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error restoring purchases', e, stackTrace);
    }
  }

  /// Handle purchase updates
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      AppLogger.debug('$_logPrefix: Purchase update: ${purchaseDetails.productID} - ${purchaseDetails.status}');
      
      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          _purchaseStateController.add(PurchaseState.purchasing);
          break;
          
        case PurchaseStatus.purchased:
        case PurchaseStatus.restored:
          _handleSuccessfulPurchase(purchaseDetails);
          break;
          
        case PurchaseStatus.error:
          _handlePurchaseError(purchaseDetails);
          break;
          
        case PurchaseStatus.canceled:
          _handlePurchaseCanceled(purchaseDetails);
          break;
      }

      // Complete the purchase
      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  /// Handle successful purchase
  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) {
    AppLogger.info('$_logPrefix: Purchase successful: ${purchaseDetails.productID}');
    
    _purchasePending = false;
    _purchases.add(purchaseDetails);
    _purchaseStateController.add(PurchaseState.purchased);
    
    // Here you would typically:
    // 1. Verify the purchase with your backend
    // 2. Grant the user access to premium features
    // 3. Update local storage/preferences
  }

  /// Handle purchase error
  void _handlePurchaseError(PurchaseDetails purchaseDetails) {
    AppLogger.error('$_logPrefix: Purchase error: ${purchaseDetails.error?.message}');
    
    _purchasePending = false;
    _purchaseStateController.add(PurchaseState.error);
  }

  /// Handle purchase cancellation
  void _handlePurchaseCanceled(PurchaseDetails purchaseDetails) {
    AppLogger.info('$_logPrefix: Purchase canceled: ${purchaseDetails.productID}');
    
    _purchasePending = false;
    _purchaseStateController.add(PurchaseState.canceled);
  }

  /// Check if user has active premium subscription
  bool hasPremiumSubscription() {
    return _purchases.any((purchase) => 
      (purchase.productID == premiumMonthlyId || purchase.productID == premiumYearlyId) &&
      purchase.status == PurchaseStatus.purchased
    );
  }

  /// Check if user has unlocked all stories
  bool hasUnlockedAllStories() {
    return _purchases.any((purchase) => 
      purchase.productID == unlockAllStoriesId &&
      purchase.status == PurchaseStatus.purchased
    );
  }

  /// Get product by ID
  ProductDetails? getProduct(String productId) {
    try {
      return _products.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// Dispose of resources
  void dispose() {
    _subscription.cancel();
    _purchaseStateController.close();
    _productsController.close();
    AppLogger.debug('$_logPrefix: Service disposed');
  }
}

/// Purchase state enum
enum PurchaseState {
  idle,
  purchasing,
  purchased,
  error,
  canceled,
}

/// Provider for in-app purchase service
final inAppPurchaseServiceProvider = Provider<InAppPurchaseService>((ref) {
  final service = InAppPurchaseService();
  ref.onDispose(() => service.dispose());
  return service;
});

/// Provider for purchase state
final purchaseStateProvider = StreamProvider<PurchaseState>((ref) {
  final service = ref.watch(inAppPurchaseServiceProvider);
  return service.purchaseStateStream;
});

/// Provider for available products
final productsProvider = StreamProvider<List<ProductDetails>>((ref) {
  final service = ref.watch(inAppPurchaseServiceProvider);
  return service.productsStream;
});

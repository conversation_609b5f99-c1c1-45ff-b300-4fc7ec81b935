import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:choice_once_upon_a_time/core/mixins/screen_narrator_mixin.dart';
import 'package:choice_once_upon_a_time/core/localization/screen_narration_service.dart';
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';
import 'package:choice_once_upon_a_time/l10n/app_localizations.dart';

// Generate mocks
@GenerateMocks([ScreenNarrationService, TTSServiceInterface])
import 'screen_narrator_mixin_test.mocks.dart';

// Test widget that uses the mixin
class TestWidget extends ConsumerStatefulWidget {
  const TestWidget({super.key});

  @override
  ConsumerState<TestWidget> createState() => _TestWidgetState();
}

class _TestWidgetState extends ConsumerState<TestWidget> with ScreenNarratorMixin {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Text('Test Widget'),
    );
  }

  // Expose mixin methods for testing
  Future<void> testPlayScreenIntroduction() {
    return playScreenIntroduction();
  }

  void testDisposeScreenNarration() {
    disposeScreenNarration();
  }

  // Test state tracking
  bool _hasPlayedIntroduction = false;
  bool _isDisposed = false;

  // Test accessors for state
  bool get testHasPlayedIntroduction => _hasPlayedIntroduction;
  bool get testIsDisposed => _isDisposed;

  @override
  Future<void> playScreenIntroduction() async {
    await super.playScreenIntroduction();
    _hasPlayedIntroduction = true;
  }

  void testResetIntroductionState() {
    _hasPlayedIntroduction = false;
  }

  void testDisposeScreenNarrator() {
    disposeScreenNarration();
    _isDisposed = true;
  }

  @override
  String Function(AppLocalizations)? getScreenIntroductionText() {
    return (localizations) => 'Test screen introduction';
  }

  // Test helper methods
  bool get testCanPlayIntroduction => true;
}

void main() {
  group('ScreenNarratorMixin', () {
    late MockScreenNarrationService mockScreenNarrationService;
    late MockTTSServiceInterface mockTTSService;

    setUp(() {
      mockScreenNarrationService = MockScreenNarrationService();
      mockTTSService = MockTTSServiceInterface();
    });

    Widget createTestWidget() {
      return ProviderScope(
        overrides: [
          screenNarrationServiceProvider.overrideWithValue(mockScreenNarrationService),
          ttsServiceProvider.overrideWithValue(mockTTSService),
        ],
        child: const MaterialApp(
          home: TestWidget(),
        ),
      );
    }

    testWidgets('should initialize with correct default state', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      final state = tester.state<_TestWidgetState>(find.byType(TestWidget));

      expect(state.testHasPlayedIntroduction, false);
      expect(state.testIsDisposed, false);
    });

    testWidgets('should play screen introduction successfully', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      final state = tester.state<_TestWidgetState>(find.byType(TestWidget));

      await state.testPlayScreenIntroduction();
      await tester.pump();

      expect(state.testHasPlayedIntroduction, true);
    });

    testWidgets('should handle narration calls', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      final state = tester.state<_TestWidgetState>(find.byType(TestWidget));

      await state.testPlayScreenIntroduction();
      await tester.pump();

      expect(state.testHasPlayedIntroduction, true);
    });

    testWidgets('should reset introduction state', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      final state = tester.state<_TestWidgetState>(find.byType(TestWidget));

      // Manually set the state to played
      state.testResetIntroductionState();
      expect(state.testHasPlayedIntroduction, false);
    });

    testWidgets('should handle disposal correctly', (WidgetTester tester) async {
      when(mockTTSService.isSpeaking).thenReturn(false);

      await tester.pumpWidget(createTestWidget());

      final state = tester.state<_TestWidgetState>(find.byType(TestWidget));

      state.testDisposeScreenNarrator();

      expect(state.testIsDisposed, true);
    });
  });
}

{"id": "pip_pantry_puzzle", "title": {"en-US": "<PERSON><PERSON> and the Pantry Puzzle"}, "coverImageUrl": "assets/images/covers/pip_pantry_puzzle.png", "targetMoralValue": "<PERSON><PERSON><PERSON>", "targetAgeSubSegment": "4-6", "version": "1.0.0", "initialSceneId": "scene_01_intro", "narratorPersonaGuidance": "Warm, gentle, and understanding 'favorite aunt' or 'kind grandparent' persona. Voice is calm, reassuring, and slightly playful, with a conspiratorial, friendly tone towards the child.", "supportedLanguages": ["en-US"], "defaultLanguage": "en-US", "assetManifestUrl": "assets/manifests/pip_pantry_puzzle_v1.json", "estimatedDurationMinutes": 8, "isFree": true, "published": true, "order": 1, "logline": {"en-US": "A curious little squirrel learns about honesty when faced with a difficult choice."}, "scenes": [{"sceneId": "scene_01_intro", "order": 1, "sceneType": "narration_illustration", "backgroundImageUrl": "assets/images/backgrounds/pip_kitchen.png", "narratorSegments": [{"id": "intro_01", "text": {"en-US": "Meet <PERSON><PERSON>, a curious little squirrel who lives in a cozy kitchen. <PERSON><PERSON> loves exploring and discovering new things every day."}, "emotionCue": "cheerful", "estimatedDurationSeconds": 5}, {"id": "intro_02", "text": {"en-US": "One morning, <PERSON><PERSON> woke up to find something very strange. The cookie jar that was always full of delicious cookies was completely empty!"}, "emotionCue": "surprised", "estimatedDurationSeconds": 6}], "nextSceneId": "scene_02_discovery"}, {"sceneId": "scene_02_discovery", "order": 2, "sceneType": "narration_illustration", "backgroundImageUrl": "assets/images/backgrounds/pip_pantry.png", "narratorSegments": [{"id": "discovery_01", "text": {"en-US": "<PERSON><PERSON> decided to investigate. As <PERSON><PERSON> looked around the kitchen, there were cookie crumbs leading to the pantry door."}, "emotionCue": "curious", "estimatedDurationSeconds": 5}], "nextSceneId": "scene_03_choice"}, {"sceneId": "scene_03_choice", "order": 3, "sceneType": "choice_point", "backgroundImageUrl": "assets/images/backgrounds/pip_pantry_door.png", "narratorSegments": [{"id": "choice_prompt", "text": {"en-US": "<PERSON><PERSON> stands in front of the pantry door. The cookie crumbs lead right under the door. What should <PERSON><PERSON> do?"}, "emotionCue": "thoughtful", "estimatedDurationSeconds": 4}], "promptTextForTTS": {"en-US": "What should <PERSON><PERSON> do next?"}, "choices": [{"id": "choice_knock", "displayTextKey": {"en-US": "Knock on the pantry door"}, "leadsToSceneId": "scene_04_knock"}, {"id": "choice_peek", "displayTextKey": {"en-US": "Quietly open the door and peek inside"}, "leadsToSceneId": "scene_05_peek"}, {"id": "choice_call", "displayTextKey": {"en-US": "Call out 'Hello? Is anyone there?'"}, "leadsToSceneId": "scene_06_call"}]}, {"sceneId": "scene_04_knock", "order": 4, "sceneType": "narration_illustration", "backgroundImageUrl": "assets/images/backgrounds/pip_pantry_door.png", "narratorSegments": [{"id": "knock_result", "text": {"en-US": "<PERSON><PERSON> politely knocked on the door. '<PERSON><PERSON>, knock!' After a moment, a small voice said, 'Come in, <PERSON><PERSON>!'"}, "emotionCue": "polite", "estimatedDurationSeconds": 5}], "nextSceneId": "scene_07_resolution"}, {"sceneId": "scene_05_peek", "order": 5, "sceneType": "narration_illustration", "backgroundImageUrl": "assets/images/backgrounds/pip_pantry_inside.png", "narratorSegments": [{"id": "peek_result", "text": {"en-US": "<PERSON><PERSON> carefully opened the door just a crack and peeked inside. There was little <PERSON> the hamster, surrounded by cookie crumbs, looking very guilty."}, "emotionCue": "sneaky", "estimatedDurationSeconds": 6}], "nextSceneId": "scene_07_resolution"}, {"sceneId": "scene_06_call", "order": 6, "sceneType": "narration_illustration", "backgroundImageUrl": "assets/images/backgrounds/pip_pantry_door.png", "narratorSegments": [{"id": "call_result", "text": {"en-US": "<PERSON><PERSON> called out in a friendly voice, 'Hello? Is anyone there?' A tiny voice replied, 'Oh no! I've been caught!' The door slowly opened."}, "emotionCue": "friendly", "estimatedDurationSeconds": 6}], "nextSceneId": "scene_07_resolution"}, {"sceneId": "scene_07_resolution", "order": 7, "sceneType": "narration_illustration", "backgroundImageUrl": "assets/images/backgrounds/pip_pantry_inside.png", "narratorSegments": [{"id": "resolution_01", "text": {"en-US": "There was <PERSON> the hamster, sitting among the cookie crumbs with tears in her eyes. 'I'm sorry, <PERSON><PERSON>,' she said. 'I was so hungry, but I was too embarrassed to ask for help.'"}, "emotionCue": "sad", "estimatedDurationSeconds": 8}, {"id": "resolution_02", "text": {"en-US": "<PERSON><PERSON> smiled kindly at <PERSON>. 'It's okay, <PERSON>. Next time, just ask! Friends always help each other. Let's bake some new cookies together!' And that's exactly what they did. The end."}, "emotionCue": "kind", "estimatedDurationSeconds": 10}]}]}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/models/scene_model.dart';
import 'package:choice_once_upon_a_time/models/choice_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_rewards_service.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';
import 'package:choice_once_upon_a_time/shared_widgets/primary_button.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator.dart';

/// Screen for playing stories with narration, subtitles, and rewards
class NarratedStoryPlayerScreen extends StatefulWidget {
  final String storyId;

  const NarratedStoryPlayerScreen({
    super.key,
    required this.storyId,
  });

  @override
  State<NarratedStoryPlayerScreen> createState() => _NarratedStoryPlayerScreenState();
}

class _NarratedStoryPlayerScreenState extends State<NarratedStoryPlayerScreen> {
  // Services
  late final StoryNarrationService _narrationService;
  late final StorySettingsService _settingsService;
  late final StoryRewardsService _rewardsService;
  late final StoryRepository _storyRepository;

  // State
  StoryModel? _story;
  SceneModel? _currentScene;
  String _currentSubtitle = '';
  NarrationProgress? _progress;
  bool _isLoading = true;
  bool _isNarrating = false;
  String? _error;
  bool _showChoices = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _loadStory();
  }

  /// Initialize all services
  void _initializeServices() {
    _narrationService = StoryNarrationService();
    _settingsService = StorySettingsService.instance;
    _rewardsService = StoryRewardsService.instance;
    _storyRepository = StoryRepository();

    // Listen to narration updates
    _narrationService.currentTextStream.listen((text) {
      if (mounted) {
        setState(() {
          _currentSubtitle = text;
        });
      }
    });

    _narrationService.progressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _progress = progress;
        });
      }
    });

    _narrationService.narrationStateStream.listen((isNarrating) {
      if (mounted) {
        setState(() {
          _isNarrating = isNarrating;
        });
        
        // Show choices when narration completes for choice scenes
        if (!isNarrating && _currentScene?.isChoicePoint == true) {
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              setState(() {
                _showChoices = true;
              });
            }
          });
        }
      }
    });
  }

  /// Load the story
  Future<void> _loadStory() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      await _settingsService.initialize();
      await _rewardsService.initialize();
      await _narrationService.initialize();

      final story = await _storyRepository.fetchStoryById(widget.storyId);
      
      setState(() {
        _story = story;
        _currentScene = story.initialScene;
        _isLoading = false;
      });

      // Start narrating the first scene
      if (_currentScene != null) {
        await _startSceneNarration(_currentScene!);
      }

    } catch (e) {
      setState(() {
        _error = 'Failed to load story: $e';
        _isLoading = false;
      });
    }
  }

  /// Start narrating a scene
  Future<void> _startSceneNarration(SceneModel scene) async {
    setState(() {
      _showChoices = false;
    });

    await _narrationService.narrateScene(scene);
  }

  /// Handle choice selection
  Future<void> _onChoiceSelected(ChoiceModel choice) async {
    setState(() {
      _showChoices = false;
    });

    // Award choice reward
    if (_story != null) {
      await _rewardsService.awardChoiceReward(
        _story!.id,
        choice.choiceId,
        choice.displayTextKey,
        _story!.targetMoralValue,
      );
    }

    // Find and navigate to next scene
    final nextScene = _story?.scenes.firstWhere(
      (scene) => scene.sceneId == choice.leadsToSceneId,
      orElse: () => throw StateError('Scene not found: ${choice.leadsToSceneId}'),
    );

    if (nextScene != null) {
      setState(() {
        _currentScene = nextScene;
      });
      await _startSceneNarration(nextScene);
    }
  }

  /// Handle scene completion (for non-choice scenes)
  Future<void> _onSceneCompleted() async {
    if (_currentScene?.nextSceneId != null) {
      final nextScene = _story?.scenes.firstWhere(
        (scene) => scene.sceneId == _currentScene!.nextSceneId,
        orElse: () => throw StateError('Scene not found: ${_currentScene!.nextSceneId}'),
      );

      if (nextScene != null) {
        setState(() {
          _currentScene = nextScene;
        });
        await _startSceneNarration(nextScene);
      }
    } else {
      // Story completed
      if (_story != null) {
        await _rewardsService.completeStory(
          _story!.id,
          _story!.title,
          _story!.targetMoralValue,
        );
      }
      _showStoryCompletedDialog();
    }
  }

  /// Show story completed dialog
  void _showStoryCompletedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Story Complete!'),
        content: Text('Congratulations! You\'ve completed "${_story?.title}".'),
        actions: [
          PrimaryButton(
            text: 'Continue',
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Return to story library
            },
          ),
        ],
      ),
    );
  }

  /// Show settings dialog
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => _SettingsDialog(settingsService: _settingsService),
    );
  }

  @override
  void dispose() {
    _narrationService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: LoadingIndicator()),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Story Player')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(_error!, style: const TextStyle(color: Colors.red)),
              const SizedBox(height: 16),
              PrimaryButton(
                text: 'Retry',
                onPressed: _loadStory,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_story?.title ?? 'Story Player'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Story content area
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Scene illustration placeholder
                  Expanded(
                    flex: 3,
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.image,
                          size: 64,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Subtitle area
                  Expanded(
                    flex: 1,
                    child: _SubtitleDisplay(
                      subtitle: _currentSubtitle,
                      settingsService: _settingsService,
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Progress bar
                  if (_progress != null)
                    _ProgressBar(progress: _progress!),
                  
                  const SizedBox(height: 16),
                  
                  // Choices or continue button
                  if (_showChoices && _currentScene?.choices != null)
                    _ChoicesWidget(
                      choices: _currentScene!.choices!,
                      onChoiceSelected: _onChoiceSelected,
                    )
                  else if (!_isNarrating && !_showChoices && _currentScene?.nextSceneId != null)
                    PrimaryButton(
                      text: 'Continue',
                      onPressed: _onSceneCompleted,
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget for displaying subtitles
class _SubtitleDisplay extends StatelessWidget {
  final String subtitle;
  final StorySettingsService settingsService;

  const _SubtitleDisplay({
    required this.subtitle,
    required this.settingsService,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<double>(
      stream: settingsService.subtitleSizeStream,
      initialData: settingsService.subtitleSize,
      builder: (context, snapshot) {
        final fontSize = snapshot.data ?? 18.0;
        
        return StreamBuilder<bool>(
          stream: settingsService.subtitlesEnabledStream,
          initialData: settingsService.subtitlesEnabled,
          builder: (context, enabledSnapshot) {
            final enabled = enabledSnapshot.data ?? true;
            
            if (!enabled) return const SizedBox.shrink();
            
            return Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.8),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                subtitle,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: fontSize,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            );
          },
        );
      },
    );
  }
}

/// Widget for displaying narration progress
class _ProgressBar extends StatelessWidget {
  final NarrationProgress progress;

  const _ProgressBar({required this.progress});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          'Sentence ${progress.currentSentence} of ${progress.totalSentences}',
          style: Theme.of(context).textTheme.bodySmall,
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress.progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }
}

/// Widget for displaying story choices
class _ChoicesWidget extends StatelessWidget {
  final List<ChoiceModel> choices;
  final Function(ChoiceModel) onChoiceSelected;

  const _ChoicesWidget({
    required this.choices,
    required this.onChoiceSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          'What would you like to do?',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        ...choices.map((choice) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => onChoiceSelected(choice),
              child: Text(choice.displayTextKey),
            ),
          ),
        )),
      ],
    );
  }
}

/// Settings dialog for subtitle and narration controls
class _SettingsDialog extends StatefulWidget {
  final StorySettingsService settingsService;

  const _SettingsDialog({required this.settingsService});

  @override
  State<_SettingsDialog> createState() => _SettingsDialogState();
}

class _SettingsDialogState extends State<_SettingsDialog> {
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Story Settings'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Subtitle size
            StreamBuilder<double>(
              stream: widget.settingsService.subtitleSizeStream,
              initialData: widget.settingsService.subtitleSize,
              builder: (context, snapshot) {
                final size = snapshot.data ?? 18.0;
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Subtitle Size: ${size.round()}'),
                    Slider(
                      value: size,
                      min: 12.0,
                      max: 32.0,
                      divisions: 20,
                      onChanged: (value) {
                        widget.settingsService.setSubtitleSize(value);
                      },
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 16),

            // Narration speed
            StreamBuilder<double>(
              stream: widget.settingsService.narrationSpeedStream,
              initialData: widget.settingsService.narrationSpeed,
              builder: (context, snapshot) {
                final speed = snapshot.data ?? 0.5;
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Narration Speed: ${(speed * 100).round()}%'),
                    Slider(
                      value: speed,
                      min: 0.1,
                      max: 1.0,
                      divisions: 9,
                      onChanged: (value) {
                        widget.settingsService.setNarrationSpeed(value);
                      },
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 16),

            // Subtitles enabled
            StreamBuilder<bool>(
              stream: widget.settingsService.subtitlesEnabledStream,
              initialData: widget.settingsService.subtitlesEnabled,
              builder: (context, snapshot) {
                final enabled = snapshot.data ?? true;
                return SwitchListTile(
                  title: const Text('Show Subtitles'),
                  value: enabled,
                  onChanged: (value) {
                    widget.settingsService.setSubtitlesEnabled(value);
                  },
                );
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

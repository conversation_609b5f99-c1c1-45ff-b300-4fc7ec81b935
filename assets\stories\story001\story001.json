{"story_id": "story001", "age_group": "3-5", "difficulty": "easy", "title": "The Lost Toy", "moral": "Kindness", "cover_image": "story_cover.jpg", "setup": {"setting": "A sunny park with green grass and tall trees", "tone": "Playful and warm", "context": "A girl learns to help her friend find a lost toy", "brief_intro": "Welcome to a sunny park adventure! Join <PERSON> and <PERSON> as they play, but oh no—<PERSON> loses his favorite teddy bear. Will <PERSON> help him find it? Let’s find out!"}, "background_music": "calm_piano.mp3", "narrator_profile": {"name": "<PERSON>", "voice": {"name": "en-US-Wavenet-A", "pitch": 0.9, "rate": 0.95, "volume": 1.0}}, "characters": [{"name": "<PERSON>", "description": "A curious girl who loves to play", "role": "Protagonist", "voice": {"name": "en-GB-Standard-A", "pitch": 1.3, "rate": 1.2, "volume": 0.9}}, {"name": "<PERSON>", "description": "<PERSON>'s friend who lost his toy", "role": "Supporting", "voice": {"name": null, "pitch": 1.4, "rate": 1.25, "volume": 0.85}}], "scenes": [{"id": "scene_1", "text": "<PERSON> and <PERSON> were playing in a sunny park when <PERSON> realized his toy was missing.", "speaker": "narrator", "emotion": "surprised", "image": "park_intro.jpg", "pause_duration": 2000, "next": "scene_2"}, {"id": "scene_2", "text": "Oh no! My teddy bear is gone!", "speaker": "<PERSON>", "emotion": "sad", "image": "alex_sad.jpg", "pause_duration": 1500, "next": "scene_3"}, {"id": "scene_3", "text": "<PERSON> saw <PERSON> looking sad. What should she do?", "speaker": "narrator", "emotion": "curious", "image": "choice_moment.jpg", "pause_duration": 0, "choices": [{"option": "Help <PERSON> find the toy", "visual": "helping_hand_icon.jpg", "next": "scene_4a"}, {"option": "Keep playing", "visual": "playing_icon.jpg", "next": "scene_4b"}]}, {"id": "scene_4a", "text": "<PERSON> said, 'Don’t worry, <PERSON>! Let’s look together.' They found the teddy bear under a bush.", "speaker": "narrator", "emotion": "happy", "image": "finding_toy.jpg", "pause_duration": 2000, "outcome": "good", "rewards": {"completion": 1, "good": 1}, "reflection": {"text": "Why was helping <PERSON> a kind thing to do?", "emotion": "curious"}}, {"id": "scene_4b", "text": "<PERSON> kept playing, and <PERSON> sat alone, feeling sad. 'I miss my teddy bear,' he whispered.", "speaker": "narrator", "emotion": "gentle", "image": "alex_alone.jpg", "pause_duration": 2000, "outcome": "bad", "rewards": {"completion": 1, "good": 0}, "reflection": {"text": "What could <PERSON> do next time to make <PERSON> happy?", "emotion": "hopeful"}}], "post_story": {"discussion": {"text": "What a kind adventure! <PERSON> learned that helping a friend can make everyone happy. What would happen if she made a different choice?", "emotion": "curious"}, "replay_prompt": {"text": "Let’s go back to the park! Want to hear <PERSON> and <PERSON>’s story again?", "emotion": "excited"}}}
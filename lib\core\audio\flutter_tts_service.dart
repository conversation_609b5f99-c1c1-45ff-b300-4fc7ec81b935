import 'dart:async';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:choice_once_upon_a_time/models/text_segment_model.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'tts_service_interface.dart';
import 'emotion_cue_mapper_service.dart';

/// Flutter TTS implementation of the TTS service interface
/// This provides on-device text-to-speech using the flutter_tts package
class FlutterTTSService implements TTSServiceInterface {
  static final FlutterTTSService _instance = FlutterTTSService._internal();
  factory FlutterTTSService() => _instance;
  FlutterTTSService._internal();

  FlutterTts? _flutterTts;
  bool _isInitialized = false;
  TTSState _currentState = TTSState.stopped;
  String? _currentLanguage;
  TTSSpeechParameters _currentParameters = const TTSSpeechParameters();

  // Stream controllers for state and progress updates
  final StreamController<TTSState> _stateController = StreamController<TTSState>.broadcast();
  final StreamController<double> _progressController = StreamController<double>.broadcast();
  final StreamController<String> _wordBoundaryController = StreamController<String>.broadcast();

  @override
  Future<bool> initialize() async {
    if (_isInitialized) {
      AppLogger.debug('TTS: Service already initialized (Instance: ${hashCode})');
      return true;
    }

    try {
      AppLogger.debug('TTS: Initializing FlutterTTSService (Instance: ${hashCode})');
      _flutterTts = FlutterTts();

      // Set up event handlers
      _flutterTts!.setStartHandler(() {
        AppLogger.debug('TTS: Speech started (Instance: ${hashCode})');
        _updateState(TTSState.playing);
      });

      _flutterTts!.setCompletionHandler(() {
        AppLogger.debug('TTS: Speech completed (Instance: ${hashCode})');
        _updateState(TTSState.stopped);
      });

      _flutterTts!.setErrorHandler((msg) {
        AppLogger.error('TTS Error (Instance: ${hashCode}): $msg');
        _updateState(TTSState.error);
      });

      _flutterTts!.setPauseHandler(() {
        AppLogger.debug('TTS: Speech paused (Instance: ${hashCode})');
        _updateState(TTSState.paused);
      });

      _flutterTts!.setContinueHandler(() {
        AppLogger.debug('TTS: Speech resumed (Instance: ${hashCode})');
        _updateState(TTSState.playing);
      });

      // Set up progress handler if available
      _flutterTts!.setProgressHandler((String text, int start, int end, String word) {
        _wordBoundaryController.add(word);
        // Calculate progress based on character position
        if (text.isNotEmpty) {
          final progress = end / text.length;
          _progressController.add(progress.clamp(0.0, 1.0));
        }
      });

      // Set default parameters
      await setLanguage('en-US');
      await setSpeechParameters(const TTSSpeechParameters());

      _isInitialized = true;
      _updateState(TTSState.stopped);
      AppLogger.info('TTS: Flutter TTS Service initialized successfully (Instance: ${hashCode})');
      return true;
    } catch (e) {
      AppLogger.error('TTS: Failed to initialize Flutter TTS (Instance: ${hashCode})', e);
      _isInitialized = false;
      _updateState(TTSState.error);
      return false;
    }
  }

  @override
  Future<bool> setLanguage(String languageCode) async {
    if (_flutterTts == null) return false;

    try {
      await _flutterTts!.setLanguage(languageCode);
      _currentLanguage = languageCode;
      AppLogger.debug('TTS: Language set to $languageCode');
      return true;
    } catch (e) {
      AppLogger.error('TTS: Error setting language to $languageCode', e);
      return false;
    }
  }

  @override
  Future<List<TTSVoice>> getAvailableVoices() async {
    if (_flutterTts == null) return [];

    try {
      final voices = await _flutterTts!.getVoices;
      return voices.map<TTSVoice>((voice) {
        return TTSVoice(
          id: voice['name'] ?? '',
          name: voice['name'] ?? '',
          language: voice['locale'] ?? '',
          gender: voice['gender'],
        );
      }).toList();
    } catch (e) {
      AppLogger.error('TTS: Error getting voices', e);
      return [];
    }
  }

  @override
  Future<List<String>> getAvailableLanguages() async {
    if (_flutterTts == null) {
      await initialize();
      if (_flutterTts == null) return [];
    }

    try {
      final languages = await _flutterTts!.getLanguages;
      AppLogger.debug('TTS: Available languages: $languages');
      return languages.cast<String>();
    } catch (e) {
      AppLogger.error('TTS: Error getting languages', e);
      return [];
    }
  }

  @override
  Future<bool> setVoice(String voiceId) async {
    if (_flutterTts == null) return false;

    try {
      await _flutterTts!.setVoice({'name': voiceId, 'locale': _currentLanguage ?? 'en-US'});
      AppLogger.debug('TTS: Voice set to $voiceId');
      return true;
    } catch (e) {
      AppLogger.error('TTS: Error setting voice to $voiceId', e);
      return false;
    }
  }

  @override
  Future<bool> setVoiceFromMap(Map<String, String> voice) async {
    if (_flutterTts == null) return false;

    try {
      await _flutterTts!.setVoice(voice);
      AppLogger.debug('TTS: Voice set from map: $voice');
      return true;
    } catch (e) {
      AppLogger.error('TTS: Error setting voice from map', e);
      return false;
    }
  }

  @override
  Future<void> setSpeechParameters(TTSSpeechParameters parameters) async {
    if (_flutterTts == null) return;

    try {
      await _flutterTts!.setSpeechRate(parameters.rate);
      await _flutterTts!.setVolume(parameters.volume);
      await _flutterTts!.setPitch(parameters.pitch);
      _currentParameters = parameters;
      AppLogger.debug('TTS: Speech parameters updated - pitch: ${parameters.pitch}, rate: ${parameters.rate}, volume: ${parameters.volume}');
    } catch (e) {
      AppLogger.error('TTS: Error setting speech parameters', e);
    }
  }

  @override
  Future<bool> speakSegment(TextSegmentModel segment, String languageCode) async {
    if (_flutterTts == null) {
      await initialize();
      if (_flutterTts == null) return false;
    }

    try {
      _updateState(TTSState.loading);

      // Get the text for the specified language
      final text = segment.getLocalizedText(languageCode);
      
      // Apply emotion-based modulation
      final emotionParams = EmotionCueMapperService.getParametersForEmotion(segment.emotionCue);
      await setSpeechParameters(emotionParams);
      
      // Speak the text
      await _flutterTts!.speak(text);
      AppLogger.debug('TTS: Speaking segment "${segment.id}" with emotion: ${segment.emotionCue}');
      return true;
    } catch (e) {
      AppLogger.error('TTS: Error speaking segment', e);
      _updateState(TTSState.error);
      return false;
    }
  }

  @override
  Future<bool> speakText(String text, {String? emotionCue}) async {
    if (_flutterTts == null) {
      await initialize();
      if (_flutterTts == null) return false;
    }

    try {
      _updateState(TTSState.loading);

      // Apply emotion-based modulation if provided
      if (emotionCue != null) {
        final emotionParams = EmotionCueMapperService.getParametersForEmotion(emotionCue);
        await setSpeechParameters(emotionParams);
      }
      
      // Speak the text
      await _flutterTts!.speak(text);
      AppLogger.debug('TTS: Speaking text with emotion: $emotionCue');
      return true;
    } catch (e) {
      AppLogger.error('TTS: Error speaking text', e);
      _updateState(TTSState.error);
      return false;
    }
  }

  /// Speak text with emotion-based voice adjustments (alias for speakText)
  Future<bool> speakWithEmotion({
    required String text,
    required String emotionCue,
    double? rate,
    double? pitch,
    double? volume,
  }) async {
    return await speakText(text, emotionCue: emotionCue);
  }

  @override
  Future<bool> speakScreenIntroduction({required String text, required String emotionCue}) async {
    AppLogger.debug("[ScreenIntroNarrator] TTS Service: speakScreenIntroduction called. _flutterTts is ${_flutterTts == null ? 'null' : 'not null'}. Text length: ${text.length}");
    if (_flutterTts == null) {
      AppLogger.warning("[ScreenIntroNarrator] TTS Service: _flutterTts is null, attempting re-initialize before speaking screen intro.");
      await initialize();
      if (_flutterTts == null) {
        AppLogger.error("[ScreenIntroNarrator] TTS Service: Re-initialization failed. Cannot speak screen introduction.");
        return false;
      }
    }

    try {
      _updateState(TTSState.loading);
      final emotionParams = EmotionCueMapperService.getParametersForEmotion(emotionCue);
      await setSpeechParameters(emotionParams);

      await _flutterTts!.speak(text);
      AppLogger.info('[ScreenIntroNarrator] TTS Service: Speaking screen introduction. Emotion: $emotionCue. Text: "${text.substring(0, text.length > 50 ? 50 : text.length)}..."');
      return true;
    } catch (e) {
      AppLogger.error('[ScreenIntroNarrator] TTS Service: Error speaking screen introduction', e);
      _updateState(TTSState.error);
      return false;
    }
  }

  @override
  Future<void> pause() async {
    if (_flutterTts != null && _currentState == TTSState.playing) {
      try {
        await _flutterTts!.pause();
        AppLogger.debug('TTS: Speech paused');
      } catch (e) {
        AppLogger.error('TTS: Error pausing speech', e);
      }
    }
  }

  @override
  Future<void> resume() async {
    if (_flutterTts != null && _currentState == TTSState.paused) {
      try {
        // Note: flutter_tts doesn't have a direct resume method on all platforms
        // This implementation may vary by platform
        AppLogger.debug('TTS: Attempting to resume speech');
        _updateState(TTSState.playing);
      } catch (e) {
        AppLogger.error('TTS: Error resuming speech', e);
      }
    }
  }

  @override
  Future<void> stop() async {
    if (_flutterTts != null) {
      try {
        await _flutterTts!.stop();
        AppLogger.debug('TTS: Speech stopped');
      } catch (e) {
        AppLogger.error('TTS: Error stopping speech', e);
      }
    }
  }

  @override
  TTSState getCurrentState() => _currentState;

  @override
  bool get isSpeaking => _currentState == TTSState.playing;

  @override
  bool get isPaused => _currentState == TTSState.paused;

  @override
  bool get isAvailable => _isInitialized && _flutterTts != null;

  @override
  double? get speechProgress => null; // Progress tracking varies by platform

  @override
  Stream<TTSState> get stateStream => _stateController.stream;

  @override
  Stream<double> get progressStream => _progressController.stream;

  @override
  Stream<String> get wordBoundaryStream => _wordBoundaryController.stream;

  @override
  Future<void> dispose() async {
    await stop();
    await _stateController.close();
    await _progressController.close();
    await _wordBoundaryController.close();
    _flutterTts = null;
    _isInitialized = false;
    _updateState(TTSState.stopped);
    AppLogger.info('TTS: Flutter TTS Service disposed');
  }

  /// Update the current state and notify listeners
  void _updateState(TTSState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      _stateController.add(newState);
    }
  }
}

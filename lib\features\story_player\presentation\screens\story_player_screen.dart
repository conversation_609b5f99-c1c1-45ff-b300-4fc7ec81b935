import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/providers/story_player_provider.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/narrator_text_display_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/narration_controls_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/choice_button_widget.dart';

/// Main story player screen for displaying story content
class StoryPlayerScreen extends ConsumerWidget {
  const StoryPlayerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final storyPlayerState = ref.watch(storyPlayerProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Story Player'),
        leading: Icon<PERSON><PERSON>on(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SafeArea(
        child: storyPlayerState.isLoading
            ? _buildLoadingState(theme)
            : storyPlayerState.error != null
                ? _buildErrorState(context, ref, storyPlayerState.error!, theme)
                : storyPlayerState.currentScene != null
                    ? _buildStoryContent(context, ref, storyPlayerState, theme)
                    : _buildEmptyState(theme),
      ),
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Loading story...',
            style: theme.textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, String error, ThemeData theme) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: isSmallScreen ? 48 : 64,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              error,
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                ref.read(storyPlayerProvider.notifier).clearError();
                context.go('/home');
              },
              child: const Text('Back to Library'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Text(
        'No story loaded',
        style: theme.textTheme.bodyLarge,
      ),
    );
  }

  Widget _buildStoryContent(BuildContext context, WidgetRef ref, StoryPlayerState state, ThemeData theme) {
    final scene = state.currentScene!;
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;
    final isLandscape = screenSize.width > screenSize.height;

    return Stack(
      children: [
        // Background
        _buildBackground(scene, theme),

        // Main content
        Column(
          children: [
            // Top bar with controls
            _buildTopBar(context, ref, theme),

            // Story content area
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 12.0 : 16.0,
                  vertical: isSmallScreen ? 8.0 : 16.0,
                ),
                child: isLandscape && !isSmallScreen
                    ? _buildLandscapeLayout(ref, state, scene, theme)
                    : _buildPortraitLayout(ref, state, scene, theme),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPortraitLayout(WidgetRef ref, StoryPlayerState state, dynamic scene, ThemeData theme) {
    return Column(
      children: [
        // Narrator text display
        Expanded(
          flex: 3,
          child: NarratorTextDisplayWidget(
            scene: scene,
            currentSegmentIndex: state.currentSegmentIndex,
          ),
        ),

        const SizedBox(height: 16),

        // Choices or narration controls
        if (scene.sceneType == 'choice_point' && scene.choices != null)
          _buildChoicesSection(ref, scene.choices!, theme)
        else
          _buildNarrationControlsSection(ref, state, theme),
      ],
    );
  }

  Widget _buildLandscapeLayout(WidgetRef ref, StoryPlayerState state, dynamic scene, ThemeData theme) {
    return Row(
      children: [
        // Narrator text display (left side)
        Expanded(
          flex: 2,
          child: NarratorTextDisplayWidget(
            scene: scene,
            currentSegmentIndex: state.currentSegmentIndex,
          ),
        ),

        const SizedBox(width: 16),

        // Choices or narration controls (right side)
        Expanded(
          flex: 1,
          child: scene.sceneType == 'choice_point' && scene.choices != null
              ? _buildChoicesSection(ref, scene.choices!, theme)
              : _buildNarrationControlsSection(ref, state, theme),
        ),
      ],
    );
  }

  Widget _buildBackground(dynamic scene, ThemeData theme) {
    // For now, display a themed background with scene ID as placeholder
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: theme.colorScheme.surface,
      child: Center(
        child: Opacity(
          opacity: 0.05,
          child: Text(
            scene.sceneId,
            style: theme.textTheme.headlineLarge?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar(BuildContext context, WidgetRef ref, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Home button
          IconButton(
            onPressed: () => _showExitConfirmation(context, ref),
            icon: const Icon(Icons.home),
            tooltip: 'Home',
          ),
          
          // Pause button
          IconButton(
            onPressed: () => _showPauseMenu(context, ref),
            icon: const Icon(Icons.pause),
            tooltip: 'Pause',
          ),
        ],
      ),
    );
  }

  Widget _buildChoicesSection(WidgetRef ref, List<dynamic> choices, ThemeData theme) {
    return Expanded(
      flex: 2,
      child: Column(
        children: [
          Text(
            'What would you like to do?',
            style: theme.textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: choices.length,
              itemBuilder: (context, index) {
                final choice = choices[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: ChoiceButtonWidget(
                    choice: choice,
                    onPressed: () {
                      ref.read(storyPlayerProvider.notifier).selectChoice(choice.choiceId);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNarrationControlsSection(WidgetRef ref, StoryPlayerState state, ThemeData theme) {
    return Column(
      children: [
        NarrationControlsWidget(
          isPlaying: state.isPlaying,
          onPlayPause: () => ref.read(storyPlayerProvider.notifier).togglePlayPause(),
          onReplay: () => ref.read(storyPlayerProvider.notifier).replayCurrentSegment(),
        ),
        const SizedBox(height: 16),
        // Next scene button for testing
        if (state.currentScene?.sceneType == 'narration_illustration')
          ElevatedButton(
            onPressed: () => ref.read(storyPlayerProvider.notifier).nextSegment(),
            child: const Text('Next'),
          ),
      ],
    );
  }

  void _showExitConfirmation(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit Story?'),
        content: const Text('Are you sure you want to exit the story? Your progress will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(storyPlayerProvider.notifier).reset();
              context.go('/home');
            },
            child: const Text('Exit'),
          ),
        ],
      ),
    );
  }

  void _showPauseMenu(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Story Paused'),
        content: const Text('What would you like to do?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Resume'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(storyPlayerProvider.notifier).reset();
              context.go('/home');
            },
            child: const Text('Exit to Library'),
          ),
        ],
      ),
    );
  }
}

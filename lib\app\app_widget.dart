import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/app/routing/app_router.dart';
import 'package:choice_once_upon_a_time/app/theme/app_theme.dart';
import 'package:choice_once_upon_a_time/widgets/global_narrator_controller.dart';
import 'package:choice_once_upon_a_time/widgets/global_narrator_widget.dart';

class AppWidget extends ConsumerWidget {
  const AppWidget({super.key});

  /// Handle device back button press
  void _handleBackButton(GoRouter router) {
    final currentLocation = router.routerDelegate.currentConfiguration.uri.toString();

    // Define navigation hierarchy
    if (currentLocation.startsWith('/parent_zone/')) {
      router.go('/parent_zone');
    } else if (currentLocation == '/parent_zone') {
      router.go('/home');
    } else if (currentLocation.startsWith('/story/')) {
      router.go('/home');
    } else if (currentLocation == '/story_library') {
      router.go('/home');
    } else if (currentLocation == '/story_player') {
      router.go('/home');
    } else if (currentLocation.startsWith('/enhanced_story_player/')) {
      router.go('/home');
    } else if (currentLocation == '/parent_gate_entry/auth') {
      router.go('/parent_gate_entry');
    } else if (currentLocation == '/parent_gate_entry') {
      router.go('/home');
    } else if (currentLocation == '/rewards') {
      router.go('/home');
    } else if (currentLocation.startsWith('/loading/')) {
      router.go('/home');
    } else {
      // For other routes, go to home
      router.go('/home');
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = AppRouter.router;

    router.routerDelegate.addListener(() {
      final route = router.routerDelegate.currentConfiguration.uri.toString();
      ref.read(globalNarratorProvider.notifier).playNarrationForRoute(route);
    });

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBackButton(router);
        }
      },
      child: MaterialApp.router(
        title: 'Choice: Once Upon A Time',
        routerConfig: router,
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        builder: (context, child) {
          return Column(
            children: [
              Expanded(child: child!),
              const GlobalNarratorWidget(),
            ],
          );
        },
      ),
    );
  }
}

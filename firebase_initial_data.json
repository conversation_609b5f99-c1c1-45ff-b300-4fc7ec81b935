{"stories": {"pip_pantry_puzzle": {"title": {"en-US": "<PERSON><PERSON> and the Pantry Puzzle"}, "logline": {"en-US": "Help <PERSON><PERSON> solve the mystery of the missing cookies and learn about honesty!"}, "coverImageUrl": "story_covers/pip_pantry_puzzle_cover_1.0.0.png", "targetMoralValue": "<PERSON><PERSON><PERSON>", "targetAgeSubSegment": "4-6", "initialSceneId": "scene_pip_01_intro", "version": "1.0.0", "published": true, "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "assetManifestUrl": "stories/pip_pantry_puzzle/1.0.0/assetManifest.json", "supportedLanguages": ["en-US"], "defaultLanguage": "en-US", "narratorPersonaGuidance": "warm, gentle, grandmotherly", "estimatedDurationMinutes": 8, "isFree": true, "order": 1}, "lila_moonpetal_wish": {"title": {"en-US": "<PERSON> and the Moonpetal Wish"}, "logline": {"en-US": "Join <PERSON> on a magical journey to help a friend in need and discover the power of empathy."}, "coverImageUrl": "story_covers/lila_moonpetal_wish_cover_1.0.0.png", "targetMoralValue": "Empathy", "targetAgeSubSegment": "5-7", "initialSceneId": "scene_lila_01_intro", "version": "1.0.0", "published": true, "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "assetManifestUrl": "stories/lila_moonpetal_wish/1.0.0/assetManifest.json", "supportedLanguages": ["en-US"], "defaultLanguage": "en-US", "narratorPersonaGuidance": "warm, gentle, encouraging", "estimatedDurationMinutes": 10, "isFree": true, "order": 2}, "finley_flying_machine": {"title": {"en-US": "Finley's Flying Machine"}, "logline": {"en-US": "Watch <PERSON><PERSON> never give up on their dream to fly and learn about perseverance."}, "coverImageUrl": "story_covers/finley_flying_machine_cover_1.0.0.png", "targetMoralValue": "Perseverance", "targetAgeSubSegment": "6-7", "initialSceneId": "scene_finley_01_intro", "version": "1.0.0", "published": true, "createdAt": "2024-01-01T00:00:00Z", "updatedAt": "2024-01-01T00:00:00Z", "assetManifestUrl": "stories/finley_flying_machine/1.0.0/assetManifest.json", "supportedLanguages": ["en-US"], "defaultLanguage": "en-US", "narratorPersonaGuidance": "encouraging, determined, supportive", "estimatedDurationMinutes": 12, "isFree": false, "order": 3}}, "app_config": {"global_settings": {"minRequiredAppVersion": "1.0.0", "isMaintenanceMode": false, "maintenanceMessage": {"en-US": "The app is currently under maintenance. Please try again later."}, "defaultMoralValuesOrder": ["<PERSON><PERSON><PERSON>", "Empathy", "Perseverance", "Kindness", "Courage", "Responsibility"]}}}
// Mocks generated by Mockito 5.4.4 from annotations
// in choice_once_upon_a_time/test/core/mixins/screen_narrator_mixin_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart'
    as _i4;
import 'package:choice_once_upon_a_time/core/localization/screen_narration_service.dart'
    as _i2;
import 'package:choice_once_upon_a_time/models/text_segment_model.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ScreenNarrationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockScreenNarrationService extends _i1.Mock
    implements _i2.ScreenNarrationService {
  MockScreenNarrationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  int get narrationCount => (super.noSuchMethod(
        Invocation.getter(#narrationCount),
        returnValue: 0,
      ) as int);

  @override
  _i3.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i2.ScreenNarration? getNarrationForScreen(String? screenKey) =>
      (super.noSuchMethod(Invocation.method(
        #getNarrationForScreen,
        [screenKey],
      )) as _i2.ScreenNarration?);

  @override
  List<String> getAvailableScreenKeys() => (super.noSuchMethod(
        Invocation.method(
          #getAvailableScreenKeys,
          [],
        ),
        returnValue: <String>[],
      ) as List<String>);
}

/// A class which mocks [TTSServiceInterface].
///
/// See the documentation for Mockito's code generation for more information.
class MockTTSServiceInterface extends _i1.Mock
    implements _i4.TTSServiceInterface {
  MockTTSServiceInterface() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isSpeaking => (super.noSuchMethod(
        Invocation.getter(#isSpeaking),
        returnValue: false,
      ) as bool);

  @override
  bool get isPaused => (super.noSuchMethod(
        Invocation.getter(#isPaused),
        returnValue: false,
      ) as bool);

  @override
  bool get isAvailable => (super.noSuchMethod(
        Invocation.getter(#isAvailable),
        returnValue: false,
      ) as bool);

  @override
  _i3.Stream<_i4.TTSState> get stateStream => (super.noSuchMethod(
        Invocation.getter(#stateStream),
        returnValue: _i3.Stream<_i4.TTSState>.empty(),
      ) as _i3.Stream<_i4.TTSState>);

  @override
  _i3.Stream<double> get progressStream => (super.noSuchMethod(
        Invocation.getter(#progressStream),
        returnValue: _i3.Stream<double>.empty(),
      ) as _i3.Stream<double>);

  @override
  _i3.Stream<String> get wordBoundaryStream => (super.noSuchMethod(
        Invocation.getter(#wordBoundaryStream),
        returnValue: _i3.Stream<String>.empty(),
      ) as _i3.Stream<String>);

  @override
  _i3.Future<bool> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> setLanguage(String? languageCode) => (super.noSuchMethod(
        Invocation.method(
          #setLanguage,
          [languageCode],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<List<_i4.TTSVoice>> getAvailableVoices() => (super.noSuchMethod(
        Invocation.method(
          #getAvailableVoices,
          [],
        ),
        returnValue: _i3.Future<List<_i4.TTSVoice>>.value(<_i4.TTSVoice>[]),
      ) as _i3.Future<List<_i4.TTSVoice>>);

  @override
  _i3.Future<List<String>> getAvailableLanguages() => (super.noSuchMethod(
        Invocation.method(
          #getAvailableLanguages,
          [],
        ),
        returnValue: _i3.Future<List<String>>.value(<String>[]),
      ) as _i3.Future<List<String>>);

  @override
  _i3.Future<bool> setVoice(String? voiceId) => (super.noSuchMethod(
        Invocation.method(
          #setVoice,
          [voiceId],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> setVoiceFromMap(Map<String, String>? voice) =>
      (super.noSuchMethod(
        Invocation.method(
          #setVoiceFromMap,
          [voice],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> setSpeechParameters(_i4.TTSSpeechParameters? parameters) =>
      (super.noSuchMethod(
        Invocation.method(
          #setSpeechParameters,
          [parameters],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<bool> speakSegment(
    _i5.TextSegmentModel? segment,
    String? languageCode,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #speakSegment,
          [
            segment,
            languageCode,
          ],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> speakText(
    String? text, {
    String? emotionCue,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #speakText,
          [text],
          {#emotionCue: emotionCue},
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> speakScreenIntroduction({
    required String? text,
    required String? emotionCue,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #speakScreenIntroduction,
          [],
          {
            #text: text,
            #emotionCue: emotionCue,
          },
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> pause() => (super.noSuchMethod(
        Invocation.method(
          #pause,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> resume() => (super.noSuchMethod(
        Invocation.method(
          #resume,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> stop() => (super.noSuchMethod(
        Invocation.method(
          #stop,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i4.TTSState getCurrentState() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentState,
          [],
        ),
        returnValue: _i4.TTSState.stopped,
      ) as _i4.TTSState);

  @override
  _i3.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}

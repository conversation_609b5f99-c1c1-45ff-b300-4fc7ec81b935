import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/app_init/presentation/screens/launch_screen.dart';
import 'package:choice_once_upon_a_time/features/app_init/presentation/screens/ftue_screen.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/screens/home_screen.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/screens/story_library_screen.dart'; // Import the new screen
import 'package:choice_once_upon_a_time/features/auth/presentation/screens/parental_gate_screen.dart';
import 'package:choice_once_upon_a_time/features/auth/presentation/screens/parent_auth_screen.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/screens/parent_zone_dashboard_screen.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/screens/sound_settings_screen.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/screens/user_profiles_screen.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/screens/progress_tracking_screen.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/screens/about_stories_screen.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/screens/manage_downloads_screen.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/screens/help_support_screen.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/screens/subscription_screen.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/screens/language_settings_screen.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/screens/story_intro_screen.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/screens/loading_screen.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/screens/story_player_screen.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/screens/calm_exit_screen.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/screens/continue_story_screen.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/screens/enhanced_story_player_screen.dart';
import 'package:choice_once_upon_a_time/features/rewards/presentation/screens/rewards_screen.dart';
import 'package:choice_once_upon_a_time/features/ai_stories/presentation/screens/ai_story_generation_screen.dart';
import 'package:choice_once_upon_a_time/screens/tts_full_test_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/launch',
    routes: <RouteBase>[
      // App initialization routes
      GoRoute(
        path: '/launch',
        builder: (BuildContext context, GoRouterState state) {
          return const LaunchScreen();
        },
      ),
      GoRoute(
        path: '/ftue',
        builder: (BuildContext context, GoRouterState state) {
          return const FTUEScreen();
        },
      ),

      // Main app routes
      GoRoute(
        path: '/home',
        builder: (BuildContext context, GoRouterState state) {
          return const HomeScreen();
        },
      ),
      GoRoute(
        path: '/story_library', // Added route for the story library
        builder: (BuildContext context, GoRouterState state) {
          return const StoryLibraryScreen();
        },
      ),
      GoRoute(
        path: '/continue_story', // Route for continue reading screen
        builder: (BuildContext context, GoRouterState state) {
          return const ContinueStoryScreen();
        },
      ),

      // Story routes
      GoRoute(
        path: '/story/:storyId',
        builder: (BuildContext context, GoRouterState state) {
          final storyId = state.pathParameters['storyId']!;
          final dataSource = state.uri.queryParameters['dataSource'] ?? 'asset';
          return StoryIntroScreen(storyId: storyId, dataSource: dataSource);
        },
      ),
      GoRoute(
        path: '/loading/:storyId/:dataSource', // Corrected path to include dataSource
        builder: (BuildContext context, GoRouterState state) {
          final storyId = state.pathParameters['storyId']!;
          final dataSource = state.pathParameters['dataSource']!;
          return LoadingScreen(storyId: storyId, dataSource: dataSource);
        },
      ),
      GoRoute(
        path: '/story_player', // Simplified route, assuming state is managed by a provider
        builder: (BuildContext context, GoRouterState state) {
          return const StoryPlayerScreen();
        },
      ),
      GoRoute(
        path: '/enhanced_story_player/:storyId',
        builder: (BuildContext context, GoRouterState state) {
          final storyId = state.pathParameters['storyId']!;
          return EnhancedStoryPlayerScreen(storyId: storyId);
        },
      ),
      GoRoute(
        path: '/rewards',
        builder: (BuildContext context, GoRouterState state) {
          return const RewardsScreen();
        },
      ),

      // Authentication and Parent Zone routes with proper nesting
      GoRoute(
        path: '/parent_gate_entry',
        builder: (BuildContext context, GoRouterState state) {
          return const ParentalGateScreen();
        },
        routes: [
          GoRoute(
            path: 'auth',
            builder: (BuildContext context, GoRouterState state) {
              return const ParentAuthScreen();
            },
          ),
        ],
      ),
      GoRoute(
        path: '/parent_zone',
        builder: (BuildContext context, GoRouterState state) {
          return const ParentZoneDashboardScreen();
        },
        routes: [
          GoRoute(
            path: 'sound_settings',
            builder: (BuildContext context, GoRouterState state) {
              return const SoundSettingsScreen();
            },
          ),
          GoRoute(
            path: 'user_profiles',
            builder: (BuildContext context, GoRouterState state) {
              return const UserProfilesScreen();
            },
          ),
          GoRoute(
            path: 'progress_tracking',
            builder: (BuildContext context, GoRouterState state) {
              return const ProgressTrackingScreen();
            },
          ),
          GoRoute(
            path: 'about_stories',
            builder: (BuildContext context, GoRouterState state) {
              return const AboutStoriesScreen();
            },
          ),
          GoRoute(
            path: 'manage_downloads',
            builder: (BuildContext context, GoRouterState state) {
              return const ManageDownloadsScreen();
            },
          ),
          GoRoute(
            path: 'help_support',
            builder: (BuildContext context, GoRouterState state) {
              return const HelpSupportScreen();
            },
          ),
          GoRoute(
            path: 'subscription',
            builder: (BuildContext context, GoRouterState state) {
              return const SubscriptionScreen();
            },
          ),
          GoRoute(
            path: 'language_settings',
            builder: (BuildContext context, GoRouterState state) {
              return const LanguageSettingsScreen();
            },
          ),
        ],
      ),
      GoRoute(
        path: '/ai_stories',
        builder: (BuildContext context, GoRouterState state) {
          return const AIStoryGenerationScreen();
        },
      ),

      // Story end and exit routes
      GoRoute(
        path: '/calm_exit',
        builder: (BuildContext context, GoRouterState state) {
          return const CalmExitScreen();
        },
      ),
      
      // Test route
      GoRoute(
        path: '/tts_test',
        builder: (BuildContext context, GoRouterState state) {
          return const TtsFullTestScreen();
        },
      ),
    ],

    // Error handling
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Page Not Found')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'Page not found',
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}

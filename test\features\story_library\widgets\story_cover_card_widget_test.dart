import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/story_cover_card_widget.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/core/storage/offline_storage_service.dart';

// Generate mocks
@GenerateMocks([OfflineStorageService])
import 'story_cover_card_widget_test.mocks.dart';

void main() {
  group('StoryCoverCardWidget Tests', () {
    late MockOfflineStorageService mockOfflineStorageService;

    setUp(() {
      mockOfflineStorageService = MockOfflineStorageService();
    });

    const testStory = StoryMetadataModel(
      id: 'test_story',
      title: {'en-US': 'Test Story Title'},
      coverImageUrl: 'https://example.com/cover.jpg',
      loglineShort: {'en-US': 'A test story logline'},
      targetMoralValue: 'kindness',
      version: '1.0',
      supportedLanguages: ['en-US'],
      defaultLanguage: 'en-US',
      initialSceneId: 'scene_1',
      estimatedSizeMb: 25,
    );

    Widget createTestWidget({
      bool isOneColumn = false,
      bool showProgress = false,
      VoidCallback? onTap,
    }) {
      return ProviderScope(
        overrides: [
          offlineStorageServiceProvider.overrideWithValue(mockOfflineStorageService),
        ],
        child: MaterialApp(
          home: Scaffold(
            body: StoryCoverCardWidget(
              story: testStory,
              isOneColumn: isOneColumn,
              showProgress: showProgress,
              onTap: onTap,
              languageCode: 'en-US',
            ),
          ),
        ),
      );
    }

    group('Basic Rendering', () {
      testWidgets('should display story title', (WidgetTester tester) async {
        // Arrange
        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Test Story Title'), findsOneWidget);
      });

      testWidgets('should display story logline', (WidgetTester tester) async {
        // Arrange
        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('A test story logline'), findsOneWidget);
      });

      testWidgets('should display story size', (WidgetTester tester) async {
        // Arrange
        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('25 MB'), findsOneWidget);
      });
    });

    group('Download States', () {
      testWidgets('should show download button when story is not downloaded', (WidgetTester tester) async {
        // Arrange
        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Download'), findsOneWidget);
        expect(find.byIcon(Icons.download_outlined), findsOneWidget);
      });

      testWidgets('should show read story button when story is downloaded', (WidgetTester tester) async {
        // Arrange
        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => true);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Read Story'), findsOneWidget);
        expect(find.byIcon(Icons.offline_pin), findsOneWidget);
      });
    });

    group('Responsive Design', () {
      testWidgets('should adapt to small screen size', (WidgetTester tester) async {
        // Arrange
        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => false);

        // Set small screen size
        await tester.binding.setSurfaceSize(const Size(375, 667)); // iPhone SE

        // Act
        await tester.pumpWidget(createTestWidget(isOneColumn: true));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(StoryCoverCardWidget), findsOneWidget);
        
        // Verify the widget renders without overflow
        expect(tester.takeException(), isNull);
      });

      testWidgets('should adapt to large screen size', (WidgetTester tester) async {
        // Arrange
        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => false);

        // Set large screen size
        await tester.binding.setSurfaceSize(const Size(1024, 768)); // iPad

        // Act
        await tester.pumpWidget(createTestWidget(isOneColumn: false));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(StoryCoverCardWidget), findsOneWidget);
        
        // Verify the widget renders without overflow
        expect(tester.takeException(), isNull);
      });
    });

    group('Progress Indicator', () {
      testWidgets('should show progress indicator when showProgress is true and story has progress', (WidgetTester tester) async {
        // Arrange
        const storyWithProgress = StoryMetadataModel(
          id: 'test_story',
          title: {'en-US': 'Test Story Title'},
          coverImageUrl: 'https://example.com/cover.jpg',
          loglineShort: {'en-US': 'A test story logline'},
          targetMoralValue: 'kindness',
          version: '1.0',
          supportedLanguages: ['en-US'],
          defaultLanguage: 'en-US',
          initialSceneId: 'scene_1',
          hasProgress: true,
        );

        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              offlineStorageServiceProvider.overrideWithValue(mockOfflineStorageService),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: StoryCoverCardWidget(
                  story: storyWithProgress,
                  isOneColumn: false,
                  showProgress: true,
                  languageCode: 'en-US',
                ),
              ),
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        // The progress indicator should be present (as a FractionallySizedBox)
        expect(find.byType(FractionallySizedBox), findsOneWidget);
      });

      testWidgets('should not show progress indicator when showProgress is false', (WidgetTester tester) async {
        // Arrange
        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createTestWidget(showProgress: false));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(FractionallySizedBox), findsNothing);
      });
    });

    group('Interactions', () {
      testWidgets('should call onTap when card is tapped', (WidgetTester tester) async {
        // Arrange
        bool tapCalled = false;
        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(createTestWidget(
          onTap: () => tapCalled = true,
        ));
        await tester.pumpAndSettle();

        await tester.tap(find.byType(StoryCoverCardWidget));
        await tester.pumpAndSettle();

        // Assert
        expect(tapCalled, isTrue);
      });

      testWidgets('should trigger download when download button is tapped', (WidgetTester tester) async {
        // Arrange
        when(mockOfflineStorageService.isStoryDownloaded('test_story'))
            .thenAnswer((_) async => false);
        when(mockOfflineStorageService.downloadStory(any, any))
            .thenAnswer((_) async => true);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        await tester.tap(find.text('Download'));
        await tester.pumpAndSettle();

        // Assert
        verify(mockOfflineStorageService.downloadStory('test_story', any)).called(1);
      });
    });

    group('Locked Stories', () {
      testWidgets('should show lock icon for locked stories', (WidgetTester tester) async {
        // Arrange
        const lockedStory = StoryMetadataModel(
          id: 'locked_story',
          title: {'en-US': 'Locked Story'},
          coverImageUrl: 'https://example.com/cover.jpg',
          loglineShort: {'en-US': 'A locked story'},
          targetMoralValue: 'kindness',
          version: '1.0',
          supportedLanguages: ['en-US'],
          defaultLanguage: 'en-US',
          initialSceneId: 'scene_1',
          isLocked: true,
        );

        when(mockOfflineStorageService.isStoryDownloaded('locked_story'))
            .thenAnswer((_) async => false);

        // Act
        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              offlineStorageServiceProvider.overrideWithValue(mockOfflineStorageService),
            ],
            child: const MaterialApp(
              home: Scaffold(
                body: StoryCoverCardWidget(
                  story: lockedStory,
                  isOneColumn: false,
                  languageCode: 'en-US',
                ),
              ),
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        expect(find.byIcon(Icons.lock), findsOneWidget);
      });
    });
  });
}

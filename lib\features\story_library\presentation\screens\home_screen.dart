import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Import the new widgets we've created
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/animated_scene_widget.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/home_screen_grid_area.dart';

/// The main home screen of the application.
///
/// This screen serves as the central hub, featuring an animated scene
/// and a scrollable grid of content sections like the story library,
/// featured stories, and the parent zone.
class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;

    // Responsive expanded height based on screen size
    final expandedHeight = screenSize.height * 0.3; // 30% of screen height

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) return;

        // Show exit confirmation dialog for root screen
        final shouldExit = await _showExitConfirmationDialog(context);
        if (shouldExit == true) {
          // Exit the app
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: expandedHeight.clamp(150.0, 300.0), // Min 150, max 300
              floating: false,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  'Choice: Once Upon A Time',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        blurRadius: 4.0,
                        color: Colors.black.withValues(alpha: 0.5),
                        offset: const Offset(1.0, 1.0),
                      ),
                    ],
                  ),
                ),
                background: const AnimatedSceneWidget(), // Our animated scene goes here
              ),
            ),
            const SliverToBoxAdapter(
              child: HomeScreenGridArea(), // The grid area with all our sections
            ),
          ],
        ),
      ),
    );
  }

  /// Show exit confirmation dialog for the home screen
  Future<bool?> _showExitConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Exit App'),
          content: const Text('Are you sure you want to exit the app?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Exit'),
            ),
          ],
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/comprehensive_voice_guide_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Welcome screen widget that introduces the story
class WelcomeScreenWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final VoidCallback onContinue;
  final StoryNarrationService narrationService;

  const WelcomeScreenWidget({
    super.key,
    required this.story,
    required this.onContinue,
    required this.narrationService,
  });

  @override
  State<WelcomeScreenWidget> createState() => _WelcomeScreenWidgetState();
}

class _WelcomeScreenWidgetState extends State<WelcomeScreenWidget>
    with TickerProviderStateMixin {
  late final AnimationController _titleController;
  late final AnimationController _contentController;
  late final Animation<double> _titleAnimation;
  late final Animation<double> _contentAnimation;
  late final Animation<Offset> _slideAnimation;

  bool _isNarrating = false;
  bool _hasNarrated = false;
  final ComprehensiveVoiceGuideService _voiceGuide = ComprehensiveVoiceGuideService.instance;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _startNarration();
  }

  void _initializeAnimations() {
    _titleController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _contentController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _titleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _titleController,
      curve: Curves.easeOutBack,
    ));

    _contentAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeOutCubic,
    ));
  }

  Future<void> _startAnimations() async {
    await _titleController.forward();
    await Future.delayed(const Duration(milliseconds: 300));
    await _contentController.forward();
  }

  Future<void> _startNarration() async {
    if (_hasNarrated) return;

    try {
      setState(() {
        _isNarrating = true;
      });

      AppLogger.debug('[SCENE_DEBUG] Starting welcome screen voice guide for story: ${widget.story.title}');

      // Play story introduction voice guide
      await _voiceGuide.playStoryIntroGuide(widget.story);

      setState(() {
        _isNarrating = false;
        _hasNarrated = true;
      });

      AppLogger.debug('[SCENE_DEBUG] Welcome screen voice guide completed');

    } catch (e) {
      AppLogger.error('[SCENE_DEBUG] Welcome screen voice guide error', e);
      setState(() {
        _isNarrating = false;
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            theme.colorScheme.primary.withOpacity(0.1),
            theme.colorScheme.surface,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
          child: Column(
            children: [
              // Story cover and title
              Expanded(
                flex: 3,
                child: AnimatedBuilder(
                  animation: _titleAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _titleAnimation.value.clamp(0.0, 1.0),
                      child: Opacity(
                        opacity: _titleAnimation.value.clamp(0.0, 1.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Story cover image
                            Container(
                              width: isSmallScreen ? 120 : 160,
                              height: isSmallScreen ? 120 : 160,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 10,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(16),
                                child: Image.asset(
                                  widget.story.coverImagePath,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: theme.colorScheme.primaryContainer,
                                      child: Icon(
                                        Icons.book,
                                        size: isSmallScreen ? 60 : 80,
                                        color: theme.colorScheme.primary,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // Story title
                            Text(
                              widget.story.title,
                              style: theme.textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.primary,
                                fontSize: isSmallScreen ? 24 : 28,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            
                            const SizedBox(height: 8),
                            
                            // Moral value badge
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.secondaryContainer,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                widget.story.moral,
                                style: theme.textTheme.labelLarge?.copyWith(
                                  color: theme.colorScheme.onSecondaryContainer,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              // Story description and setup
              Expanded(
                flex: 2,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: AnimatedBuilder(
                    animation: _contentAnimation,
                    builder: (context, child) {
                      return Opacity(
                        opacity: _contentAnimation.value.clamp(0.0, 1.0),
                        child: child!,
                      );
                    },
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                        // Brief intro or context
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: theme.colorScheme.outline.withValues(alpha: 0.2),
                            ),
                          ),
                          child: Column(
                            children: [
                              if (widget.story.setup.briefIntro != null) ...[
                                Text(
                                  widget.story.setup.briefIntro!,
                                  style: theme.textTheme.bodyLarge?.copyWith(
                                    fontSize: isSmallScreen ? 16 : 18,
                                    height: 1.5,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                              ],
                              
                              // Story details
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildInfoChip(
                                    icon: Icons.child_care,
                                    label: widget.story.ageGroup,
                                    theme: theme,
                                  ),
                                  _buildInfoChip(
                                    icon: Icons.timer,
                                    label: '${widget.story.scenes.length} scenes',
                                    theme: theme,
                                  ),
                                  _buildInfoChip(
                                    icon: Icons.star,
                                    label: widget.story.difficulty,
                                    theme: theme,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Narration status
                        if (_isNarrating)
                          Column(
                            children: [
                              const CircularProgressIndicator(),
                              const SizedBox(height: 8),
                              Text(
                                'Preparing your story...',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              
              // Continue button
              AnimatedBuilder(
                animation: _contentAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(0, 50 * (1 - _contentAnimation.value.clamp(0.0, 1.0))),
                    child: Opacity(
                      opacity: _contentAnimation.value.clamp(0.0, 1.0),
                      child: SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _hasNarrated ? widget.onContinue : null,
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                              vertical: isSmallScreen ? 16 : 20,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Text(
                            'Meet the Characters',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 16 : 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required ThemeData theme,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colorScheme.onPrimaryContainer,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: theme.textTheme.labelSmall?.copyWith(
              color: theme.colorScheme.onPrimaryContainer,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

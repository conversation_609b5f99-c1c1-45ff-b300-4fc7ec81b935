// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Choice: Once Upon A Time';

  @override
  String get ftueScreenWelcome => 'Welcome to Choice! Let me show you how to start your interactive story adventure.';

  @override
  String get ftueFeatureInteractiveStories => 'Our interactive stories let you choose your own path in every tale.';

  @override
  String get ftueFeatureLifeValues => 'These stories teach and inspire, reinforcing positive life values.';

  @override
  String get ftueFeatureNarration => 'Listen to your stories come alive with our empathetic narrator.';

  @override
  String get ftueCompletePrompt => 'Are you ready? Tap \'Start Reading\' to begin!';

  @override
  String get ftueSkipPrompt => 'Or, if you\'re ready, you can skip the tutorial.';

  @override
  String get storyIntroGenericPrompt => 'Welcome to the story introduction. Please review the details and decide to start or download the story.';

  @override
  String get errorStoryNotFound => 'Sorry, we could not find the details for this story.';

  @override
  String get downloadInProgressPleaseWait => 'Download is in progress. Please wait.';

  @override
  String get errorDownloadFailed => 'Download failed. Please try again.';

  @override
  String get downloadCompletePrompt => 'Story downloaded successfully and is now available offline!';

  @override
  String get storyLockedPrompt => 'This story is locked. You can unlock it with a premium subscription.';

  @override
  String get storyIntroActionStartOfflinePrompt => 'The story is available offline. You can start reading now or manage your downloads.';

  @override
  String get storyIntroActionStartDownloadPrompt => 'You can start the story now, or download it to make it available offline.';

  @override
  String get storyIntroDownloadConfirmationTitle => 'Download Story?';

  @override
  String get storyIntroDownloadConfirmationMessage => 'Would you like to download this story for offline reading?';

  @override
  String get storyIntroNotNowButton => 'Not Now';

  @override
  String get storyIntroDownloadButton => 'Download';

  @override
  String get storyIntroPremiumRequiredTitle => 'Premium Required';

  @override
  String get storyIntroPremiumRequiredMessage => 'This story is part of our premium collection. Subscribe to unlock all stories and features.';

  @override
  String get storyIntroCancelButton => 'Cancel';

  @override
  String get storyIntroSubscribeButton => 'Subscribe';

  @override
  String get homeScreenIntro => 'Welcome to your Story Library. Browse the stories below and tap on one to begin your adventure!';

  @override
  String get refreshingStories => 'Refreshing your stories...';

  @override
  String get loadingStories => 'Loading your stories, please wait.';

  @override
  String errorLoadingStories(Object errorMessage) {
    return 'Oops, there was an error loading your stories: $errorMessage';
  }

  @override
  String get noStoriesAvailable => 'There are currently no stories available. Please check back later or try refreshing.';

  @override
  String get parentalGateScreenIntro => 'Welcome to the Parent Zone access screen. This area is for parents and guardians only.';

  @override
  String get parentalGateHoldButtonPrompt => 'To continue, please press and hold the button below for 3 seconds.';

  @override
  String get parentalGateKeepHoldingPrompt => 'Keep holding... Almost there!';

  @override
  String get parentalGatePassedPrompt => 'Access granted. Loading Parent Zone.';

  @override
  String get aiStoryGenerationScreenIntro => 'Welcome to the AI Story Creator! Here you can create personalized stories just for your child.';

  @override
  String get aiStoryGeneratingPrompt => 'Creating your personalized story now. This may take a moment, so please be patient.';

  @override
  String get aiStoryGeneratedPrompt => 'Your story is ready! Let\'s start reading your personalized adventure.';

  @override
  String get aiStoryGenerationErrorPrompt => 'Sorry, there was an issue creating your story. Please check your settings and try again.';
}
